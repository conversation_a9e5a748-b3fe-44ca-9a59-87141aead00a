from flask import Blueprint, request, jsonify, render_template
from app import db
from app.models import TestRunHistory, TestTask
import json
from datetime import datetime
import os

history_bp = Blueprint('history', __name__)

@history_bp.route('/')
def index():
    """测试运行历史列表页面"""
    return render_template('history/index.html')

@history_bp.route('/view/<int:history_id>')
def view(history_id):
    """测试运行历史详情页面"""
    return render_template('history/view.html', history_id=history_id)

@history_bp.route('/api/list')
def list_history():
    """获取测试运行历史列表"""
    history = TestRunHistory.query.order_by(TestRunHistory.start_time.desc()).all()
    return jsonify([h.to_dict() for h in history])

@history_bp.route('/api/get/<int:history_id>')
def get_history(history_id):
    """获取测试运行历史详情"""
    history = TestRunHistory.query.get_or_404(history_id)
    return jsonify(history.to_dict())

@history_bp.route('/api/get_by_run_id/<path:run_id>')
def get_history_by_run_id(run_id):
    """通过运行ID获取测试运行历史详情"""
    history = TestRunHistory.query.filter_by(run_id=run_id).first_or_404()
    return jsonify(history.to_dict())

# @history_bp.route('/api/results/<int:history_id>')
# def get_history_results(history_id):
#     """获取测试运行历史结果"""
#     results = RunResult.query.filter_by(history_id=history_id).all()
#     return jsonify([r.to_dict() for r in results])

# @history_bp.route('/api/messages/<int:history_id>')
# def get_history_messages(history_id):
#     """获取测试运行历史消息样本"""
#     messages = RunLatestMessage.query.filter_by(history_id=history_id).all()
#     return jsonify([m.to_dict() for m in messages])

@history_bp.route('/api/rerun/<int:history_id>', methods=['POST'])
def rerun_history(history_id):
    """重新运行测试"""
    from app.utils.kafka_client import KafkaClient
    import uuid
    
    # 获取历史记录
    history = TestRunHistory.query.get_or_404(history_id)
    
    # 获取任务
    task = TestTask.query.get_or_404(history.task_id)
    task_config = task.to_dict()
    
    # 获取环境变量
    env_vars = json.loads(history.environment_snapshot) if history.environment_snapshot else {}
    
    # 获取消息模板
    from app.models import MessageTemplate
    template_ids = [queue['template_id'] for queue in task_config.get('queues', [])]
    templates = MessageTemplate.query.filter(MessageTemplate.id.in_(template_ids)).all()
    templates_data = [t.to_dict() for t in templates]
    
    # 获取Kafka服务器配置
    kafka_servers = env_vars.get('KAFKA_SERVERS', 'localhost:9092')
    
    # 设置环境变量
    for key, value in env_vars.items():
        os.environ[key] = value
    
    # 生成新的运行ID
    run_id = str(uuid.uuid4())
    
    # 创建新的测试运行历史记录
    new_history = TestRunHistory(
        run_id=run_id,
        task_id=task.id,
        environment_id=history.environment_id,
        start_time=datetime.utcnow(),
        status='running',
        environment_snapshot=history.environment_snapshot,
        task_snapshot=history.task_snapshot
    )
    db.session.add(new_history)
    db.session.commit()
    
    # 启动测试
    kafka_client = KafkaClient()
    success, error = kafka_client.start_test(
        run_id=run_id,
        task_config=task_config,
        environment_vars=env_vars,
        message_templates=templates_data,
        kafka_servers=kafka_servers
    )
    
    if not success:
        # 更新历史记录状态
        new_history.status = 'failed'
        new_history.end_time = datetime.utcnow()
        db.session.commit()
        return jsonify({'success': False, 'error': error}), 400
    
    return jsonify({
        'success': True,
        'run_id': run_id,
        'history_id': new_history.id
    })

@history_bp.route('/api/delete/<int:history_id>', methods=['DELETE'])
def delete_history(history_id):
    """删除测试运行历史"""
    history = TestRunHistory.query.get_or_404(history_id)
    db.session.delete(history)
    db.session.commit()
    return jsonify({'success': True}) 