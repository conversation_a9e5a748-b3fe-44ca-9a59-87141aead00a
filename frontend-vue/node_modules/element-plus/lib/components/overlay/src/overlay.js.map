{"version": 3, "file": "overlay.js", "sources": ["../../../../../../packages/components/overlay/src/overlay.ts"], "sourcesContent": ["import { createVNode, defineComponent, h, renderSlot } from 'vue'\nimport { PatchFlags, buildProps, definePropType } from '@element-plus/utils'\nimport { useNamespace, useSameTarget } from '@element-plus/hooks'\n\nimport type { CSSProperties, ExtractPropTypes } from 'vue'\nimport type { ZIndexProperty } from 'csstype'\n\nexport const overlayProps = buildProps({\n  mask: {\n    type: Boolean,\n    default: true,\n  },\n  customMaskEvent: Boolean,\n  overlayClass: {\n    type: definePropType<string | string[] | Record<string, boolean>>([\n      String,\n      Array,\n      Object,\n    ]),\n  },\n  zIndex: {\n    type: definePropType<ZIndexProperty>([String, Number]),\n  },\n} as const)\nexport type OverlayProps = ExtractPropTypes<typeof overlayProps>\n\nexport const overlayEmits = {\n  click: (evt: MouseEvent) => evt instanceof MouseEvent,\n}\nexport type OverlayEmits = typeof overlayEmits\n\nconst BLOCK = 'overlay'\n\nexport default defineComponent({\n  name: 'ElOverlay',\n\n  props: overlayProps,\n  emits: overlayEmits,\n\n  setup(props, { slots, emit }) {\n    // No reactivity on this prop because when its rendering with a global\n    // component, this will be a constant flag.\n    const ns = useNamespace(BLOCK)\n\n    const onMaskClick = (e: MouseEvent) => {\n      emit('click', e)\n    }\n\n    const { onClick, onMousedown, onMouseup } = useSameTarget(\n      props.customMaskEvent ? undefined : onMaskClick\n    )\n\n    // init here\n    return () => {\n      // when the vnode meets the same structure but with different change trigger\n      // it will not automatically update, thus we simply use h function to manage updating\n      return props.mask\n        ? createVNode(\n            'div',\n            {\n              class: [ns.b(), props.overlayClass],\n              style: {\n                zIndex: props.zIndex,\n              },\n              onClick,\n              onMousedown,\n              onMouseup,\n            },\n            [renderSlot(slots, 'default')],\n            PatchFlags.STYLE | PatchFlags.CLASS | PatchFlags.PROPS,\n            ['onClick', 'onMouseup', 'onMousedown']\n          )\n        : h(\n            'div',\n            {\n              class: props.overlayClass,\n              style: {\n                zIndex: props.zIndex,\n                position: 'fixed',\n                top: '0px',\n                right: '0px',\n                bottom: '0px',\n                left: '0px',\n              } as CSSProperties,\n            },\n            [renderSlot(slots, 'default')]\n          )\n    }\n  },\n})\n"], "names": ["buildProps", "definePropType", "defineComponent", "useNamespace", "useSameTarget", "createVNode", "renderSlot", "PatchFlags", "h"], "mappings": ";;;;;;;;;;AAGY,MAAC,YAAY,GAAGA,kBAAU,CAAC;AACvC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,eAAe,EAAE,OAAO;AAC1B,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAEC,sBAAc,CAAC;AACzB,MAAM,MAAM;AACZ,MAAM,KAAK;AACX,MAAM,MAAM;AACZ,KAAK,CAAC;AACN,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAEA,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,GAAG;AACH,CAAC,EAAE;AACS,MAAC,YAAY,GAAG;AAC5B,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC3C,EAAE;AACF,MAAM,KAAK,GAAG,SAAS,CAAC;AACxB,cAAeC,mBAAe,CAAC;AAC/B,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,KAAK,EAAE,YAAY;AACrB,EAAE,KAAK,EAAE,YAAY;AACrB,EAAE,KAAK,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;AAChC,IAAI,MAAM,EAAE,GAAGC,kBAAY,CAAC,KAAK,CAAC,CAAC;AACnC,IAAI,MAAM,WAAW,GAAG,CAAC,CAAC,KAAK;AAC/B,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AACvB,KAAK,CAAC;AACN,IAAI,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,GAAGC,qBAAa,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC,CAAC;AAC5G,IAAI,OAAO,MAAM;AACjB,MAAM,OAAO,KAAK,CAAC,IAAI,GAAGC,eAAW,CAAC,KAAK,EAAE;AAC7C,QAAQ,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,YAAY,CAAC;AAC3C,QAAQ,KAAK,EAAE;AACf,UAAU,MAAM,EAAE,KAAK,CAAC,MAAM;AAC9B,SAAS;AACT,QAAQ,OAAO;AACf,QAAQ,WAAW;AACnB,QAAQ,SAAS;AACjB,OAAO,EAAE,CAACC,cAAU,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,EAAEC,gBAAU,CAAC,KAAK,GAAGA,gBAAU,CAAC,KAAK,GAAGA,gBAAU,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC,GAAGC,KAAC,CAAC,KAAK,EAAE;AACrJ,QAAQ,KAAK,EAAE,KAAK,CAAC,YAAY;AACjC,QAAQ,KAAK,EAAE;AACf,UAAU,MAAM,EAAE,KAAK,CAAC,MAAM;AAC9B,UAAU,QAAQ,EAAE,OAAO;AAC3B,UAAU,GAAG,EAAE,KAAK;AACpB,UAAU,KAAK,EAAE,KAAK;AACtB,UAAU,MAAM,EAAE,KAAK;AACvB,UAAU,IAAI,EAAE,KAAK;AACrB,SAAS;AACT,OAAO,EAAE,CAACF,cAAU,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;AACzC,KAAK,CAAC;AACN,GAAG;AACH,CAAC,CAAC;;;;;;"}