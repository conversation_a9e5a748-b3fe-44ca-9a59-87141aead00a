import type { CSSProperties, StyleValue } from 'vue';
import type { UsePopperReturn } from 'element-plus/es/hooks';
import type { UsePopperContentReturn } from './use-content';
import type { PopperContentProps } from '../content';
export declare const usePopperContentDOM: (props: PopperContentProps, { attributes, styles, role, }: Pick<UsePopperReturn, "attributes" | "styles"> & Pick<UsePopperContentReturn, "role">) => {
    ariaModal: import("vue").ComputedRef<string | undefined>;
    arrowStyle: import("vue").ComputedRef<CSSProperties>;
    contentAttrs: import("vue").ComputedRef<{
        [key: string]: string | boolean;
    }>;
    contentClass: import("vue").ComputedRef<((string | {
        [x: string]: boolean;
    } | (string | {
        [x: string]: boolean;
    } | (string | {
        [x: string]: boolean;
    } | (string | {
        [x: string]: boolean;
    } | (string | {
        [x: string]: boolean;
    } | (string | {
        [x: string]: boolean;
    } | (string | {
        [x: string]: boolean;
    } | (string | {
        [x: string]: boolean;
    } | (string | {
        [x: string]: boolean;
    } | (string | {
        [x: string]: boolean;
    } | (string | {
        [x: string]: boolean;
    } | (string | {
        [x: string]: boolean;
    } | any)[])[])[])[])[])[])[])[])[])[])[]) | undefined)[]>;
    contentStyle: import("vue").ComputedRef<StyleValue[]>;
    contentZIndex: import("vue").Ref<number>;
    updateZIndex: () => void;
};
export type UsePopperContentDOMReturn = ReturnType<typeof usePopperContentDOM>;
