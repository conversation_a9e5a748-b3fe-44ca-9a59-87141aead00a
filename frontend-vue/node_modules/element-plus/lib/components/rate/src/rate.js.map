{"version": 3, "file": "rate.js", "sources": ["../../../../../../packages/components/rate/src/rate.ts"], "sourcesContent": ["import { Star, StarFilled } from '@element-plus/icons-vue'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport {\n  buildProps,\n  definePropType,\n  iconPropType,\n  isNumber,\n  mutable,\n} from '@element-plus/utils'\nimport { useAriaProps, useSizeProp } from '@element-plus/hooks'\nimport type { Component, ExtractPropTypes } from 'vue'\nimport type Rate from './rate.vue'\n\nexport const rateProps = buildProps({\n  /**\n   * @description binding value\n   */\n  modelValue: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description native `id` attribute\n   */\n  id: {\n    type: String,\n    default: undefined,\n  },\n  /**\n   * @description threshold value between low and medium level. The value itself will be included in low level\n   */\n  lowThreshold: {\n    type: Number,\n    default: 2,\n  },\n  /**\n   * @description threshold value between medium and high level. The value itself will be included in high level\n   */\n  highThreshold: {\n    type: Number,\n    default: 4,\n  },\n  /**\n   * @description max rating score\n   */\n  max: {\n    type: Number,\n    default: 5,\n  },\n  /**\n   * @description colors for icons. If array, it should have 3 elements, each of which corresponds with a score level, else if object, the key should be threshold value between two levels, and the value should be corresponding color\n   */\n  colors: {\n    type: definePropType<string[] | Record<number, string>>([Array, Object]),\n    default: () => mutable(['', '', ''] as const),\n  },\n  /**\n   * @description color of unselected icons\n   */\n  voidColor: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description color of unselected read-only icons\n   */\n  disabledVoidColor: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description icon components. If array, it should have 3 elements, each of which corresponds with a score level, else if object, the key should be threshold value between two levels, and the value should be corresponding icon component\n   */\n  icons: {\n    type: definePropType<\n      Array<string | Component> | Record<number, string | Component>\n    >([Array, Object]),\n    default: () =>\n      [StarFilled, StarFilled, StarFilled] as [Component, Component, Component],\n  },\n  /**\n   * @description component of unselected icons\n   */\n  voidIcon: {\n    type: iconPropType,\n    default: () => Star as Component,\n  },\n  /**\n   * @description component of unselected read-only icons\n   */\n  disabledVoidIcon: {\n    type: iconPropType,\n    default: () => StarFilled as Component,\n  },\n  /**\n   * @description whether Rate is read-only\n   */\n  disabled: Boolean,\n  /**\n   * @description whether picking half start is allowed\n   */\n  allowHalf: Boolean,\n  /**\n   * @description whether to display texts\n   */\n  showText: Boolean,\n  /**\n   * @description whether to display current score. show-score and show-text cannot be true at the same time\n   */\n  showScore: Boolean,\n  /**\n   * @description color of texts\n   */\n  textColor: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description text array\n   */\n  texts: {\n    type: definePropType<string[]>(Array),\n    default: () =>\n      mutable([\n        'Extremely bad',\n        'Disappointed',\n        'Fair',\n        'Satisfied',\n        'Surprise',\n      ] as const),\n  },\n  /**\n   * @description score template\n   */\n  scoreTemplate: {\n    type: String,\n    default: '{value}',\n  },\n  /**\n   * @description size of Rate\n   */\n  size: useSizeProp,\n  /**\n   * @description whether value can be reset to `0`\n   */\n  clearable: Boolean,\n  ...useAriaProps(['ariaLabel']),\n} as const)\n\nexport type RateProps = ExtractPropTypes<typeof rateProps>\n\nexport const rateEmits = {\n  [CHANGE_EVENT]: (value: number) => isNumber(value),\n  [UPDATE_MODEL_EVENT]: (value: number) => isNumber(value),\n}\nexport type RateEmits = typeof rateEmits\n\nexport type RateInstance = InstanceType<typeof Rate> & unknown\n"], "names": ["buildProps", "definePropType", "mutable", "StarFilled", "iconPropType", "Star", "useSizeProp", "useAriaProps", "CHANGE_EVENT", "isNumber", "UPDATE_MODEL_EVENT"], "mappings": ";;;;;;;;;;;;;AAUY,MAAC,SAAS,GAAGA,kBAAU,CAAC;AACpC,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAEC,sBAAc,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACzC,IAAI,OAAO,EAAE,MAAMC,kBAAO,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACxC,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,iBAAiB,EAAE;AACrB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAED,sBAAc,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACzC,IAAI,OAAO,EAAE,MAAM,CAACE,mBAAU,EAAEA,mBAAU,EAAEA,mBAAU,CAAC;AACvD,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAEC,iBAAY;AACtB,IAAI,OAAO,EAAE,MAAMC,aAAI;AACvB,GAAG;AACH,EAAE,gBAAgB,EAAE;AACpB,IAAI,IAAI,EAAED,iBAAY;AACtB,IAAI,OAAO,EAAE,MAAMD,mBAAU;AAC7B,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEF,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAMC,kBAAO,CAAC;AAC3B,MAAM,eAAe;AACrB,MAAM,cAAc;AACpB,MAAM,MAAM;AACZ,MAAM,WAAW;AACjB,MAAM,UAAU;AAChB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,SAAS;AACtB,GAAG;AACH,EAAE,IAAI,EAAEI,iBAAW;AACnB,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,GAAGC,oBAAY,CAAC,CAAC,WAAW,CAAC,CAAC;AAChC,CAAC,EAAE;AACS,MAAC,SAAS,GAAG;AACzB,EAAE,CAACC,kBAAY,GAAG,CAAC,KAAK,KAAKC,cAAQ,CAAC,KAAK,CAAC;AAC5C,EAAE,CAACC,wBAAkB,GAAG,CAAC,KAAK,KAAKD,cAAQ,CAAC,KAAK,CAAC;AAClD;;;;;"}