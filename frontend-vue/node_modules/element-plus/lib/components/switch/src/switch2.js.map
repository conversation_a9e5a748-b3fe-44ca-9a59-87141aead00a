{"version": 3, "file": "switch2.js", "sources": ["../../../../../../packages/components/switch/src/switch.vue"], "sourcesContent": ["<template>\n  <div :class=\"switchKls\" @click.prevent=\"switchValue\">\n    <input\n      :id=\"inputId\"\n      ref=\"input\"\n      :class=\"ns.e('input')\"\n      type=\"checkbox\"\n      role=\"switch\"\n      :aria-checked=\"checked\"\n      :aria-disabled=\"switchDisabled\"\n      :aria-label=\"ariaLabel\"\n      :name=\"name\"\n      :true-value=\"activeValue\"\n      :false-value=\"inactiveValue\"\n      :disabled=\"switchDisabled\"\n      :tabindex=\"tabindex\"\n      @change=\"handleChange\"\n      @keydown.enter=\"switchValue\"\n    />\n    <span\n      v-if=\"!inlinePrompt && (inactiveIcon || inactiveText)\"\n      :class=\"labelLeftKls\"\n    >\n      <el-icon v-if=\"inactiveIcon\">\n        <component :is=\"inactiveIcon\" />\n      </el-icon>\n      <span v-if=\"!inactiveIcon && inactiveText\" :aria-hidden=\"checked\">{{\n        inactiveText\n      }}</span>\n    </span>\n    <span ref=\"core\" :class=\"ns.e('core')\" :style=\"coreStyle\">\n      <div v-if=\"inlinePrompt\" :class=\"ns.e('inner')\">\n        <template v-if=\"activeIcon || inactiveIcon\">\n          <el-icon :class=\"ns.is('icon')\">\n            <component :is=\"checked ? activeIcon : inactiveIcon\" />\n          </el-icon>\n        </template>\n        <template v-else-if=\"activeText || inactiveText\">\n          <span :class=\"ns.is('text')\" :aria-hidden=\"!checked\">\n            {{ checked ? activeText : inactiveText }}\n          </span>\n        </template>\n      </div>\n      <div :class=\"ns.e('action')\">\n        <el-icon v-if=\"loading\" :class=\"ns.is('loading')\">\n          <loading />\n        </el-icon>\n        <slot v-else-if=\"checked\" name=\"active-action\">\n          <el-icon v-if=\"activeActionIcon\">\n            <component :is=\"activeActionIcon\" />\n          </el-icon>\n        </slot>\n        <slot v-else-if=\"!checked\" name=\"inactive-action\">\n          <el-icon v-if=\"inactiveActionIcon\">\n            <component :is=\"inactiveActionIcon\" />\n          </el-icon>\n        </slot>\n      </div>\n    </span>\n    <span\n      v-if=\"!inlinePrompt && (activeIcon || activeText)\"\n      :class=\"labelRightKls\"\n    >\n      <el-icon v-if=\"activeIcon\">\n        <component :is=\"activeIcon\" />\n      </el-icon>\n      <span v-if=\"!activeIcon && activeText\" :aria-hidden=\"!checked\">{{\n        activeText\n      }}</span>\n    </span>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, nextTick, onMounted, ref, watch } from 'vue'\nimport {\n  addUnit,\n  debugWarn,\n  isBoolean,\n  isPromise,\n  throwError,\n} from '@element-plus/utils'\nimport ElIcon from '@element-plus/components/icon'\nimport {\n  useFormDisabled,\n  useFormItem,\n  useFormItemInputId,\n  useFormSize,\n} from '@element-plus/components/form'\nimport { Loading } from '@element-plus/icons-vue'\nimport {\n  CHANGE_EVENT,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { useNamespace } from '@element-plus/hooks'\nimport { switchEmits, switchProps } from './switch'\nimport type { CSSProperties } from 'vue'\n\nconst COMPONENT_NAME = 'ElSwitch'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\nconst props = defineProps(switchProps)\nconst emit = defineEmits(switchEmits)\n\nconst { formItem } = useFormItem()\nconst switchSize = useFormSize()\nconst ns = useNamespace('switch')\n\nconst { inputId } = useFormItemInputId(props, {\n  formItemContext: formItem,\n})\n\nconst switchDisabled = useFormDisabled(computed(() => props.loading))\nconst isControlled = ref(props.modelValue !== false)\nconst input = ref<HTMLInputElement>()\nconst core = ref<HTMLSpanElement>()\n\nconst switchKls = computed(() => [\n  ns.b(),\n  ns.m(switchSize.value),\n  ns.is('disabled', switchDisabled.value),\n  ns.is('checked', checked.value),\n])\n\nconst labelLeftKls = computed(() => [\n  ns.e('label'),\n  ns.em('label', 'left'),\n  ns.is('active', !checked.value),\n])\n\nconst labelRightKls = computed(() => [\n  ns.e('label'),\n  ns.em('label', 'right'),\n  ns.is('active', checked.value),\n])\n\nconst coreStyle = computed<CSSProperties>(() => ({\n  width: addUnit(props.width),\n}))\n\nwatch(\n  () => props.modelValue,\n  () => {\n    isControlled.value = true\n  }\n)\n\nconst actualValue = computed(() => {\n  return isControlled.value ? props.modelValue : false\n})\n\nconst checked = computed(() => actualValue.value === props.activeValue)\n\nif (![props.activeValue, props.inactiveValue].includes(actualValue.value)) {\n  emit(UPDATE_MODEL_EVENT, props.inactiveValue)\n  emit(CHANGE_EVENT, props.inactiveValue)\n  emit(INPUT_EVENT, props.inactiveValue)\n}\n\nwatch(checked, (val) => {\n  input.value!.checked = val\n\n  if (props.validateEvent) {\n    formItem?.validate?.('change').catch((err) => debugWarn(err))\n  }\n})\n\nconst handleChange = () => {\n  const val = checked.value ? props.inactiveValue : props.activeValue\n  emit(UPDATE_MODEL_EVENT, val)\n  emit(CHANGE_EVENT, val)\n  emit(INPUT_EVENT, val)\n  nextTick(() => {\n    input.value!.checked = checked.value\n  })\n}\n\nconst switchValue = () => {\n  if (switchDisabled.value) return\n\n  const { beforeChange } = props\n  if (!beforeChange) {\n    handleChange()\n    return\n  }\n\n  const shouldChange = beforeChange()\n\n  const isPromiseOrBool = [\n    isPromise(shouldChange),\n    isBoolean(shouldChange),\n  ].includes(true)\n  if (!isPromiseOrBool) {\n    throwError(\n      COMPONENT_NAME,\n      'beforeChange must return type `Promise<boolean>` or `boolean`'\n    )\n  }\n\n  if (isPromise(shouldChange)) {\n    shouldChange\n      .then((result) => {\n        if (result) {\n          handleChange()\n        }\n      })\n      .catch((e) => {\n        debugWarn(COMPONENT_NAME, `some error occurred: ${e}`)\n      })\n  } else if (shouldChange) {\n    handleChange()\n  }\n}\n\nconst focus = (): void => {\n  input.value?.focus?.()\n}\n\nonMounted(() => {\n  input.value!.checked = checked.value\n})\n\ndefineExpose({\n  /**\n   *  @description manual focus to the switch component\n   **/\n  focus,\n  /**\n   * @description whether Switch is checked\n   */\n  checked,\n})\n</script>\n"], "names": ["useFormItem", "useFormSize", "useNamespace", "useFormItemInputId", "useFormDisabled", "computed", "ref", "addUnit", "watch", "UPDATE_MODEL_EVENT", "INPUT_EVENT", "debugWarn", "CHANGE_EVENT", "nextTick", "isPromise", "isBoolean", "throwError", "onMounted", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_withModifiers", "_createElementVNode"], "mappings": ";;;;;;;;;;;;;;;;;;;uCAoGc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;;AAKA,IAAM,MAAA,EAAE,QAAS,EAAA,GAAIA,uBAAY,EAAA,CAAA;AACjC,IAAA,MAAM,aAAaC,8BAAY,EAAA,CAAA;AAC/B,IAAM,MAAA,EAAA,GAAKC,mBAAa,QAAQ,CAAA,CAAA;AAEhC,IAAA,MAAM,EAAE,OAAA,EAAY,GAAAC,8BAAA,CAAmB,KAAO,EAAA;AAAA,MAC5C,eAAiB,EAAA,QAAA;AAAA,KAClB,CAAA,CAAA;AAED,IAAA,MAAM,iBAAiBC,kCAAgB,CAAAC,YAAA,CAAS,MAAM,KAAA,CAAM,OAAO,CAAC,CAAA,CAAA;AACpE,IAAA,MAAM,YAAe,GAAAC,OAAA,CAAI,KAAM,CAAA,UAAA,KAAe,KAAK,CAAA,CAAA;AACnD,IAAA,MAAM,QAAQA,OAAsB,EAAA,CAAA;AACpC,IAAA,MAAM,OAAOA,OAAqB,EAAA,CAAA;AAElC,IAAM,MAAA,SAAA,GAAYD,aAAS,MAAM;AAAA,MAC/B,GAAG,CAAE,EAAA;AAAA,MACL,EAAA,CAAG,CAAE,CAAA,UAAA,CAAW,KAAK,CAAA;AAAA,MACrB,EAAG,CAAA,EAAA,CAAG,UAAY,EAAA,cAAA,CAAe,KAAK,CAAA;AAAA,MACtC,EAAG,CAAA,EAAA,CAAG,SAAW,EAAA,OAAA,CAAQ,KAAK,CAAA;AAAA,KAC/B,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAeA,aAAS,MAAM;AAAA,MAClC,EAAA,CAAG,EAAE,OAAO,CAAA;AAAA,MACZ,EAAA,CAAG,EAAG,CAAA,OAAA,EAAS,MAAM,CAAA;AAAA,MACrB,EAAG,CAAA,EAAA,CAAG,QAAU,EAAA,CAAC,QAAQ,KAAK,CAAA;AAAA,KAC/B,CAAA,CAAA;AAED,IAAM,MAAA,aAAA,GAAgBA,aAAS,MAAM;AAAA,MACnC,EAAA,CAAG,EAAE,OAAO,CAAA;AAAA,MACZ,EAAA,CAAG,EAAG,CAAA,OAAA,EAAS,OAAO,CAAA;AAAA,MACtB,EAAG,CAAA,EAAA,CAAG,QAAU,EAAA,OAAA,CAAQ,KAAK,CAAA;AAAA,KAC9B,CAAA,CAAA;AAED,IAAM,MAAA,SAAA,GAAYA,aAAwB,OAAO;AAAA,MAC/C,KAAA,EAAOE,aAAQ,CAAA,KAAA,CAAM,KAAK,CAAA;AAAA,KAC1B,CAAA,CAAA,CAAA;AAEF,IAAAC,SAAA,CAAA,MAAA,KAAA,CAAA,UAAA,EAAA,MAAA;AAAA,MACE,YAAY,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,KAAA,CACZ,CAAM;AACJ,IAAA,MAAA,WAAa,GAAQH,YAAA,CAAA,MAAA;AAAA,MACvB,OAAA,YAAA,CAAA,KAAA,GAAA,KAAA,CAAA,UAAA,GAAA,KAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAEA,IAAM,MAAA,OAAA,GAAAA,mBAA6B,WAAA,CAAA,KAAA,KAAA,KAAA,CAAA,WAAA,CAAA,CAAA;AACjC,IAAO,IAAA,CAAA,CAAA,KAAA,CAAA,WAAqB,EAAA,KAAA,CAAA,aAAmB,CAAA,CAAA,QAAA,CAAA,WAAA,CAAA,KAAA,CAAA,EAAA;AAAA,MAChD,IAAA,CAAAI,wBAAA,EAAA,KAAA,CAAA,aAAA,CAAA,CAAA;AAED,MAAA,IAAM,mBAAmB,EAAA,KAAA,CAAA,aAAkB,CAAA,CAAA;AAE3C,MAAI,IAAE,CAAAC,iBAAmB,EAAA,KAAA,CAAA;AACvB,KAAK;AACL,IAAKF,SAAA,CAAA,OAAA,EAAA,CAAA,GAAA;AACL,MAAK,IAAA,EAAA,CAAA;AAAgC,MACvC,KAAA,CAAA,KAAA,CAAA,OAAA,GAAA,GAAA,CAAA;AAEA,MAAM,IAAA,KAAA,CAAA,aAAkB,EAAA;AACtB,QAAA,CAAA,EAAA,WAAuB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAAG,eAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAEvB,OAAA;AACE,KAAU,CAAA,CAAA;AAAkD,IAC9D,MAAA,YAAA,GAAA,MAAA;AAAA,MACD,MAAA,GAAA,GAAA,OAAA,CAAA,KAAA,GAAA,KAAA,CAAA,aAAA,GAAA,KAAA,CAAA,WAAA,CAAA;AAED,MAAA,IAAM,yBAAqB,EAAA,GAAA,CAAA,CAAA;AACzB,MAAA,IAAA,CAAAC,kBAAY,EAAA,GAAgB,CAAA,CAAA;AAC5B,MAAA,IAAA,CAAKF;AACL,MAAAG;AACA,QAAA,mBAAqB,GAAA,OAAA,CAAA,KAAA,CAAA;AACrB,OAAA,CAAA,CAAA;AACE,KAAM,CAAA;AAAyB,IAAA,MAChC,WAAA,GAAA,MAAA;AAAA,MACH,IAAA,cAAA,CAAA,KAAA;AAEA,QAAA;AACE,MAAA,oBAA0B,EAAA,GAAA,KAAA,CAAA;AAE1B,MAAM,IAAA,CAAA;AACN,QAAA,YAAmB,EAAA,CAAA;AACjB,QAAa,OAAA;AACb,OAAA;AAAA,MACF,MAAA,YAAA,GAAA,YAAA,EAAA,CAAA;AAEA,MAAA,MAAM,eAAe,GAAa;AAElC,QAAAC,gBAAwB,CAAA,YAAA,CAAA;AAAA,QACtBC,gBAAU,YAAY,CAAA;AAAA,OAAA,CACtB,aAAsB,CAAA,CAAA;AAAA,MACxB,oBAAe,EAAA;AACf,QAAAC,gBAAsB,CAAA,cAAA,EAAA,+DAAA,CAAA,CAAA;AACpB,OAAA;AAAA,MACE,IAAAF,gBAAA,CAAA,YAAA,CAAA,EAAA;AAAA,QACA,YAAA,CAAA,IAAA,CAAA,CAAA,MAAA,KAAA;AAAA,UACF,IAAA,MAAA,EAAA;AAAA,YACF,YAAA,EAAA,CAAA;AAEA,WAAI;AACF,SACG,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,KAAA;AACC,UAAAH,eAAY,CAAA,cAAA,EAAA,CAAA,qBAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACV,SAAa,CAAA,CAAA;AAAA,OACf,MAAA,IAAA,YAAA,EAAA;AAAA,QACF,YACc,EAAA,CAAA;AACZ,OAAU;AAA2C,KAAA,CAAA;AACtD,IAAA,cACoB,MAAA;AACvB,MAAa,IAAA,EAAA,EAAA,EAAA,CAAA;AAAA,MACf,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAAA,KACF,CAAA;AAEA,IAAAM,oBAA0B;AACxB,MAAA,KAAA,CAAM,aAAe,GAAA,OAAA,CAAA,KAAA,CAAA;AAAA,KACvB,CAAA,CAAA;AAEA,IAAA,MAAA,CAAA;AACE,MAAM,KAAA;AAAyB,MAChC,OAAA;AAED,KAAa,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,QAAA,KAAA,EAAAC,kBAAA,CAAAC,SAAA,CAAA,SAAA,CAAA,CAAA;AAAA,QAIX,OAAA,EAAAC,iBAAA,CAAA,WAAA,EAAA,CAAA,SAAA,CAAA,CAAA;AAAA,OAAA,EAAA;AAAA,QAAAC,sBAAA,CAAA,OAAA,EAAA;AAAA,UAAA,EAAA,EAAAF,SAAA,CAAA,OAAA,CAAA;AAAA,UAIA,OAAA,EAAA,OAAA;AAAA,UACD,GAAA,EAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}