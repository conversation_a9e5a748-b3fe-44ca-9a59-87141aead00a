{"version": 3, "file": "utils.js", "sources": ["../../../../../../packages/components/watermark/src/utils.ts"], "sourcesContent": ["import type { CSSProperties } from 'vue'\n\n/** converting camel-cased strings to be lowercase and link it with Separato */\nexport function toLowercaseSeparator(key: string) {\n  return key.replace(/([A-Z])/g, '-$1').toLowerCase()\n}\n\nexport function getStyleStr(style: CSSProperties): string {\n  return Object.keys(style)\n    .map(\n      (key) =>\n        `${toLowercaseSeparator(key)}: ${style[key as keyof CSSProperties]};`\n    )\n    .join(' ')\n}\n\n/** Returns the ratio of the device's physical pixel resolution to the css pixel resolution */\nexport function getPixelRatio() {\n  return window.devicePixelRatio || 1\n}\n\n/** Whether to re-render the watermark */\nexport const reRendering = (\n  mutation: MutationRecord,\n  watermarkElement?: HTMLElement\n) => {\n  let flag = false\n  // Whether to delete the watermark node\n  if (mutation.removedNodes.length && watermarkElement) {\n    flag = Array.from(mutation.removedNodes).includes(watermarkElement)\n  }\n  // Whether the watermark dom property value has been modified\n  if (mutation.type === 'attributes' && mutation.target === watermarkElement) {\n    flag = true\n  }\n  return flag\n}\n"], "names": [], "mappings": ";;;;AAAO,SAAS,oBAAoB,CAAC,GAAG,EAAE;AAC1C,EAAE,OAAO,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;AACtD,CAAC;AACM,SAAS,WAAW,CAAC,KAAK,EAAE;AACnC,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,oBAAoB,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnG,CAAC;AACM,SAAS,aAAa,GAAG;AAChC,EAAE,OAAO,MAAM,CAAC,gBAAgB,IAAI,CAAC,CAAC;AACtC,CAAC;AACW,MAAC,WAAW,GAAG,CAAC,QAAQ,EAAE,gBAAgB,KAAK;AAC3D,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC;AACnB,EAAE,IAAI,QAAQ,CAAC,YAAY,CAAC,MAAM,IAAI,gBAAgB,EAAE;AACxD,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;AACxE,GAAG;AACH,EAAE,IAAI,QAAQ,CAAC,IAAI,KAAK,YAAY,IAAI,QAAQ,CAAC,MAAM,KAAK,gBAAgB,EAAE;AAC9E,IAAI,IAAI,GAAG,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd;;;;;;;"}