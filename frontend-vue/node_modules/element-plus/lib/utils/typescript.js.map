{"version": 3, "file": "typescript.js", "sources": ["../../../../packages/utils/typescript.ts"], "sourcesContent": ["export const mutable = <T extends readonly any[] | Record<string, unknown>>(\n  val: T\n) => val as Mutable<typeof val>\nexport type Mutable<T> = { -readonly [P in keyof T]: T[P] }\n\nexport type HTMLElementCustomized<T> = HTMLElement & T\n\n/**\n * @deprecated stop to use null\n * @see {@link https://github.com/sindresorhus/meta/discussions/7}\n */\nexport type Nullable<T> = T | null\n\nexport type Arrayable<T> = T | T[]\nexport type Awaitable<T> = Promise<T> | T\n"], "names": [], "mappings": ";;;;AAAY,MAAC,OAAO,GAAG,CAAC,GAAG,KAAK;;;;"}