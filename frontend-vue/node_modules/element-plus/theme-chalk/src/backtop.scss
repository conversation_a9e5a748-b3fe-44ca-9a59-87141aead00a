@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(backtop) {
  @include set-component-css-var('backtop', $backtop);

  position: fixed;
  background-color: getCssVar('backtop', 'bg-color');
  width: 40px;
  height: 40px;
  border-radius: 50%;
  color: getCssVar('backtop', 'text-color');
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: getCssVar('box-shadow', 'lighter');
  cursor: pointer;
  z-index: 5;

  &:hover {
    background-color: getCssVar('backtop', 'hover-bg-color');
  }

  @include e(icon) {
    font-size: 20px;
  }
}
