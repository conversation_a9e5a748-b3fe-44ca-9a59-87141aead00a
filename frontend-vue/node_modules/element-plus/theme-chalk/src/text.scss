@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(text) {
  @include set-component-css-var('text', $text);
}

@include b(text) {
  align-self: center;
  margin: 0;
  padding: 0;
  font-size: getCssVar('text', 'font-size');
  color: getCssVar('text', 'color');
  overflow-wrap: break-word;

  @include when(truncated) {
    display: inline-block;
    max-width: 100%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  @include when(line-clamp) {
    display: -webkit-inline-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  @each $size in (large, default, small) {
    @include m($size) {
      @include set-css-var-value(
        ('text', 'font-size'),
        map.get($text-font-size, $size)
      );
    }
  }

  @each $type in $types {
    &.#{bem('text', '', $type)} {
      @include css-var-from-global(('text', 'color'), ('color', $type));
    }
  }

  & > .#{bem('icon')} {
    vertical-align: -2px;
  }
}
