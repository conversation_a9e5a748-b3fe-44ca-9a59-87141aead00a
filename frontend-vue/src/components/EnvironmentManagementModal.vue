<template>
  <div v-if="visible" class="modal" @click.self="handleClose">
    <div class="modal-content">
      <div class="modal-header">
        <h4>环境变量管理</h4>
        <button class="modal-close-btn" @click="handleClose">&times;</button>
      </div>
      <div class="modal-body">
        <div class="modal-split-layout">
          <!-- Left Panel - Environment List -->
          <div class="modal-left-panel">
            <div class="modal-left-header">
              <h5>环境列表</h5>
              <button class="btn-sm btn-primary" @click="createNewEnvironment">新建</button>
            </div>
            <div class="modal-left-content">
              <div class="env-list">
                <div 
                  v-for="env in environments" 
                  :key="env.id"
                  class="env-list-item"
                  :class="{ active: selectedEnv?.id === env.id }"
                  @click="selectEnvironment(env)"
                >
                  <span>{{ env.name }}</span>
                  <span class="env-actions">
                    <button title="复制" @click.stop="copyEnvironment(env)">📋</button>
                    <button title="删除" @click.stop="deleteEnvironment(env)">🗑</button>
                  </span>
                </div>
              </div>
            </div>
            <div class="modal-left-footer">
              <button class="btn-sm btn-secondary" style="width: 100%;" @click="importEnvironment">
                导入环境...
              </button>
            </div>
          </div>

          <!-- Right Panel - Environment Editor -->
          <div class="modal-right-panel">
            <div class="modal-right-header">
              <h5>环境详情</h5>
            </div>
            <div class="modal-right-content">
              <div class="form-group">
                <label for="envName">环境名称</label>
                <input 
                  type="text" 
                  id="envName" 
                  v-model="editingEnv.name"
                  style="width: 100%;"
                  placeholder="输入环境名称"
                />
              </div>
              <div class="form-group">
                <label for="envDesc">描述 (可选)</label>
                <input 
                  type="text" 
                  id="envDesc" 
                  v-model="editingEnv.description"
                  style="width: 100%;"
                  placeholder="输入环境描述"
                />
              </div>
              <div class="form-group">
                <div class="env-vars-header">
                  <label for="envVars">环境变量 (K=V 格式, 每行一个)</label>
                  <div class="env-vars-actions">
                    <button type="button" @click="previewEnvVars" class="preview-btn">
                      👁 预览
                    </button>
                    <button type="button" @click="validateEnvVars" class="validate-btn">
                      ✓ 验证
                    </button>
                  </div>
                </div>
                <textarea
                  id="envVarsTextarea"
                  v-model="editingEnv.variables"
                  rows="12"
                  style="width: 100%; font-family: monospace; font-size: 0.9em;"
                  placeholder="KAFKA_BROKERS=kafka-dev:9092&#10;SCHEMA_REGISTRY_URL=http://schema-registry-dev:8081&#10;TIMEOUT=5000"
                ></textarea>

                <!-- 预览区域 -->
                <div v-if="showPreview" class="env-preview">
                  <div class="env-preview-header">
                    <span>环境变量预览</span>
                    <button type="button" @click="showPreview = false" class="close-preview">✕</button>
                  </div>
                  <div class="env-preview-content">
                    <div v-if="parsedEnvVars.length === 0" class="no-vars">
                      暂无有效的环境变量
                    </div>
                    <div v-for="(envVar, index) in parsedEnvVars" :key="index" class="env-var-item">
                      <span class="env-key">{{ envVar.key }}</span>
                      <span class="env-separator">=</span>
                      <span class="env-value">{{ envVar.value }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn-secondary" @click="handleClose">取消</button>
        <button class="btn-primary" @click="saveEnvironment">保存更改</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = ref(false)
const selectedEnv = ref(null)
const showPreview = ref(false)
const parsedEnvVars = ref([])

// 模拟环境数据
const environments = ref([
  {
    id: 1,
    name: '开发环境 (DEV)',
    description: '本地开发环境配置',
    variables: 'KAFKA_BROKERS=kafka-dev:9092\nSCHEMA_REGISTRY_URL=http://schema-registry-dev:8081\nTIMEOUT=5000\nCLIENT_ID=dev-client\nGROUP_ID=dev-group'
  },
  {
    id: 2,
    name: '测试环境 (TEST)',
    description: '测试环境配置',
    variables: 'KAFKA_BROKERS=kafka-test:9092\nSCHEMA_REGISTRY_URL=http://schema-registry-test:8081\nTIMEOUT=8000\nCLIENT_ID=test-client\nGROUP_ID=test-group'
  },
  {
    id: 3,
    name: '预发环境 (STAGING)',
    description: '预发布环境配置',
    variables: 'KAFKA_BROKERS=kafka-staging:9092\nSCHEMA_REGISTRY_URL=http://schema-registry-staging:8081\nTIMEOUT=10000\nCLIENT_ID=staging-client\nGROUP_ID=staging-group'
  },
  {
    id: 4,
    name: '生产环境 (PROD)',
    description: '生产环境配置',
    variables: 'KAFKA_BROKERS=kafka-prod:9092\nSCHEMA_REGISTRY_URL=http://schema-registry-prod:8081\nTIMEOUT=15000\nCLIENT_ID=prod-client\nGROUP_ID=prod-group'
  }
])

const editingEnv = reactive({
  id: null,
  name: '',
  description: '',
  variables: ''
})

watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && environments.value.length > 0) {
    selectEnvironment(environments.value[0])
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const selectEnvironment = (env) => {
  selectedEnv.value = env
  Object.assign(editingEnv, { ...env })
}

const createNewEnvironment = () => {
  const newEnv = {
    id: Date.now(),
    name: '新环境',
    description: '',
    variables: ''
  }
  environments.value.push(newEnv)
  selectEnvironment(newEnv)
}

const copyEnvironment = (env) => {
  const copiedEnv = {
    id: Date.now(),
    name: env.name + ' (副本)',
    description: env.description,
    variables: env.variables
  }
  environments.value.push(copiedEnv)
  selectEnvironment(copiedEnv)
  ElMessage.success('环境复制成功')
}

const deleteEnvironment = async (env) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除环境 "${env.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = environments.value.findIndex(e => e.id === env.id)
    if (index > -1) {
      environments.value.splice(index, 1)
      if (selectedEnv.value?.id === env.id) {
        if (environments.value.length > 0) {
          selectEnvironment(environments.value[0])
        } else {
          selectedEnv.value = null
          Object.assign(editingEnv, { id: null, name: '', description: '', variables: '' })
        }
      }
    }
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除环境失败:', error)
    }
  }
}

const importEnvironment = () => {
  ElMessage.info('导入功能待实现')
}

const saveEnvironment = () => {
  if (!editingEnv.name.trim()) {
    ElMessage.error('请输入环境名称')
    return
  }
  
  if (editingEnv.id) {
    // 更新现有环境
    const index = environments.value.findIndex(e => e.id === editingEnv.id)
    if (index > -1) {
      environments.value[index] = { ...editingEnv }
      selectedEnv.value = environments.value[index]
    }
  } else {
    // 创建新环境
    const newEnv = { ...editingEnv, id: Date.now() }
    environments.value.push(newEnv)
    selectedEnv.value = newEnv
  }
  
  ElMessage.success('保存成功')
}

// 环境变量预览和验证
const previewEnvVars = () => {
  parseEnvVars()
  showPreview.value = true
}

const validateEnvVars = () => {
  const lines = editingEnv.variables.split('\n').filter(line => line.trim())
  let validCount = 0
  let invalidLines = []

  lines.forEach((line, index) => {
    const trimmed = line.trim()
    if (trimmed && !trimmed.startsWith('#')) {
      if (trimmed.includes('=')) {
        const [key] = trimmed.split('=', 2)
        if (key.trim()) {
          validCount++
        } else {
          invalidLines.push(index + 1)
        }
      } else {
        invalidLines.push(index + 1)
      }
    }
  })

  if (invalidLines.length === 0) {
    ElMessage.success(`验证通过！共 ${validCount} 个有效环境变量`)
  } else {
    ElMessage.warning(`发现 ${invalidLines.length} 行格式错误：第 ${invalidLines.join(', ')} 行`)
  }
}

const parseEnvVars = () => {
  const lines = editingEnv.variables.split('\n')
  const parsed = []

  lines.forEach(line => {
    const trimmed = line.trim()
    if (trimmed && !trimmed.startsWith('#') && trimmed.includes('=')) {
      const [key, ...valueParts] = trimmed.split('=')
      const value = valueParts.join('=') // 处理值中包含=的情况
      if (key.trim()) {
        parsed.push({
          key: key.trim(),
          value: value || ''
        })
      }
    }
  })

  parsedEnvVars.value = parsed
}

const handleClose = () => {
  visible.value = false
  showPreview.value = false
}
</script>

<style scoped>
/* Modal基础样式 */
.modal {
  display: flex;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.4);
  align-items: center;
  justify-content: center;
}

.modal-content {
  background-color: #fefefe;
  border: 1px solid #888;
  width: 80%;
  max-width: 1000px;
  border-radius: 5px;
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
  display: flex;
  flex-direction: column;
  max-height: 80vh;
}

.modal-header {
  padding: 15px 20px;
  background-color: #2c3e50;
  color: #ecf0f1;
  border-bottom: 1px solid #34495e;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.modal-header h4 {
  margin: 0;
  font-size: 1.2em;
}

.modal-close-btn {
  color: #aaa;
  font-size: 28px;
  font-weight: bold;
  background: none;
  border: none;
  cursor: pointer;
}

.modal-close-btn:hover,
.modal-close-btn:focus {
  color: #ecf0f1;
  text-decoration: none;
}

.modal-body {
  padding: 20px;
  flex-grow: 1;
  overflow: hidden; /* 不显示滚动条 */
  min-height: 400px;
}

.modal-footer {
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-top: 1px solid #e0e0e0;
  text-align: right;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.modal-footer button {
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 10px;
  border: 1px solid;
}

.btn-primary {
  background-color: #3498db;
  color: white;
  border-color: #3498db;
}

.btn-secondary {
  background-color: #e0e0e0;
  color: #333;
  border-color: #ccc;
}

/* 分割布局 */
.modal-split-layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 20px;
  height: 500px;
}

.modal-left-panel {
  border-right: 1px solid #e0e0e0;
  padding-right: 15px;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 重要：允许flex子项收缩 */
}

.modal-left-header {
  flex-shrink: 0; /* 头部不收缩 */
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-left-header h5 {
  margin: 0;
}

.btn-sm {
  padding: 4px 8px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.8em;
}

.btn-sm.btn-primary {
  background: #3498db;
  color: white;
}

.btn-sm.btn-secondary {
  background: #f0f0f0;
  border: 1px solid #ddd;
  color: #333;
}

.modal-left-content {
  flex: 1;
  min-height: 0; /* 重要：允许内容区域收缩 */
  overflow: hidden;
}

.env-list {
  border: 1px solid #ddd;
  border-radius: 4px;
  height: 100%;
  overflow-y: auto;
}

.env-list-item {
  padding: 10px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s;
}

.env-list-item:hover {
  background-color: #f5f5f5;
}

.env-list-item.active {
  background-color: #e3f2fd;
  border-left: 3px solid #3498db;
}

.env-actions button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.9em;
  margin-left: 5px;
  padding: 2px 4px;
}

.env-actions button:hover {
  background-color: rgba(0,0,0,0.1);
  border-radius: 2px;
}

.modal-left-footer {
  flex-shrink: 0; /* 底部不收缩 */
  margin-top: 15px;
}

.modal-right-panel {
  display: flex;
  flex-direction: column;
}

.modal-right-header h5 {
  margin-top: 0;
}

.modal-right-content {
  flex: 1;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  font-size: 0.9em;
}

.form-group input,
.form-group textarea {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 0.95em;
}

.form-group textarea {
  resize: vertical;
}

/* 环境变量预览样式 */
.env-vars-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.env-vars-actions {
  display: flex;
  gap: 8px;
}

.preview-btn, .validate-btn {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 3px;
  background: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.preview-btn:hover {
  background-color: #e3f2fd;
  border-color: #3498db;
}

.validate-btn:hover {
  background-color: #e8f5e8;
  border-color: #27ae60;
}

.env-preview {
  margin-top: 10px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  background: #f8f9fa;
  max-height: 200px;
  overflow-y: auto;
}

.env-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #e9ecef;
  border-bottom: 1px solid #dee2e6;
  font-weight: 500;
  font-size: 14px;
}

.close-preview {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-preview:hover {
  color: #333;
  background-color: rgba(0,0,0,0.1);
  border-radius: 50%;
}

.env-preview-content {
  padding: 8px 12px;
}

.no-vars {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

.env-var-item {
  display: flex;
  align-items: center;
  padding: 4px 0;
  font-family: monospace;
  font-size: 13px;
  border-bottom: 1px solid #f0f0f0;
}

.env-var-item:last-child {
  border-bottom: none;
}

.env-key {
  color: #2c3e50;
  font-weight: 500;
  min-width: 120px;
  flex-shrink: 0;
}

.env-separator {
  color: #666;
  margin: 0 8px;
}

.env-value {
  color: #27ae60;
  flex: 1;
  word-break: break-all;
}
</style>
