<template>
  <el-dialog
    v-model="visible"
    title="新建任务"
    width="800px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="任务名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入任务名称" />
      </el-form-item>
      
      <el-form-item label="任务描述" prop="description">
        <el-input 
          v-model="form.description" 
          type="textarea" 
          :rows="3"
          placeholder="请输入任务描述"
        />
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="周期时长(秒)" prop="cycle_duration">
            <el-input-number 
              v-model="form.cycle_duration" 
              :min="1" 
              :max="3600"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="总时长(秒)" prop="total_duration">
            <el-input-number 
              v-model="form.total_duration" 
              :min="0" 
              :max="86400"
              style="width: 100%"
            />
            <div class="form-tip">0表示无限运行</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="环境配置" prop="environment_id">
        <el-select
          v-model="form.environment_id"
          placeholder="请选择环境配置"
          style="width: 100%"
        >
          <el-option
            v-for="env in environments"
            :key="env.id"
            :label="env.name"
            :value="env.id"
          />
        </el-select>
      </el-form-item>

      <!-- 队列配置 -->
      <el-form-item label="队列配置">
        <div class="queue-config-section">
          <div class="queue-header">
            <span>队列列表</span>
            <el-button type="primary" size="small" @click="addQueue">
              添加队列
            </el-button>
          </div>

          <div v-if="form.queues.length === 0" class="no-queues">
            <p>暂无队列配置，请添加至少一个队列</p>
          </div>

          <div v-for="(queue, index) in form.queues" :key="index" class="queue-item">
            <div class="queue-item-header">
              <span>队列 {{ index + 1 }}</span>
              <el-button type="danger" size="small" @click="removeQueue(index)">
                删除
              </el-button>
            </div>

            <el-row :gutter="10">
              <el-col :span="12">
                <label>消息模板</label>
                <el-select
                  v-model="queue.template_id"
                  placeholder="选择模板"
                  style="width: 100%"
                  @change="onTemplateChange(index)"
                >
                  <el-option
                    v-for="template in templates"
                    :key="template.id"
                    :label="template.name"
                    :value="template.id"
                  />
                </el-select>
              </el-col>
              <el-col :span="12">
                <label>Topic</label>
                <el-input
                  v-model="queue.topic"
                  placeholder="自动从模板获取"
                  readonly
                  style="background-color: #f5f7fa;"
                />
              </el-col>
            </el-row>

            <el-row :gutter="10" style="margin-top: 10px;">
              <el-col :span="12">
                <label>设备数量</label>
                <el-input-number
                  v-model="queue.device_count"
                  :min="1"
                  :max="1000"
                  style="width: 100%"
                />
              </el-col>
              <el-col :span="12">
                <label>每周期消息数</label>
                <el-input-number
                  v-model="queue.messages_per_cycle"
                  :min="1"
                  :max="100"
                  style="width: 100%"
                />
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          创建任务
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { tasksApi } from '@/api/tasks'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'created'])

const visible = ref(false)
const formRef = ref()
const submitting = ref(false)
const environments = ref([])
const templates = ref([])

const form = reactive({
  name: '',
  description: '',
  cycle_duration: 60,
  total_duration: 300,
  environment_id: null,
  queues: []
})

const rules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 1, max: 64, message: '长度在 1 到 64 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 256, message: '长度不能超过 256 个字符', trigger: 'blur' }
  ],
  cycle_duration: [
    { required: true, message: '请输入周期时长', trigger: 'blur' }
  ],
  total_duration: [
    { required: true, message: '请输入总时长', trigger: 'blur' }
  ],
  environment_id: [
    { required: true, message: '请选择环境配置', trigger: 'change' }
  ]
}

watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    loadEnvironments()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
  if (!val) {
    resetForm()
  }
})

const loadEnvironments = async () => {
  try {
    environments.value = await tasksApi.getEnvironments()
  } catch (error) {
    console.error('加载环境列表失败:', error)
  }
}

const loadTemplates = async () => {
  try {
    templates.value = await tasksApi.getTemplates()
  } catch (error) {
    console.error('加载模板列表失败:', error)
  }
}

const resetForm = () => {
  Object.assign(form, {
    name: '',
    description: '',
    cycle_duration: 60,
    total_duration: 300,
    environment_id: null,
    queues: []
  })
  formRef.value?.clearValidate()
}

// 队列管理方法
const addQueue = () => {
  form.queues.push({
    template_id: null,
    topic: '',
    device_count: 10,
    messages_per_cycle: 5
  })
}

const removeQueue = (index) => {
  form.queues.splice(index, 1)
}

const onTemplateChange = (queueIndex) => {
  const queue = form.queues[queueIndex]
  const template = templates.value.find(t => t.id === queue.template_id)
  if (template) {
    queue.topic = template.topic
  }
}

const handleClose = () => {
  visible.value = false
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitting.value = true
    await tasksApi.create(form)
    
    ElMessage.success('任务创建成功')
    emit('created')
    handleClose()
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      console.error('创建任务失败:', error)
    }
  } finally {
    submitting.value = false
  }
}

onMounted(() => {
  if (props.modelValue) {
    loadEnvironments()
    loadTemplates()
  }
})
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}

/* 队列配置样式 */
.queue-config-section {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.queue-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-weight: 500;
}

.no-queues {
  text-align: center;
  color: #909399;
  padding: 20px;
}

.queue-item {
  background-color: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
}

.queue-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-weight: 500;
}

.queue-item label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #606266;
}
</style>
