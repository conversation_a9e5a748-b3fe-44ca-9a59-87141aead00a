<template>
  <div v-if="visible" class="modal" @click.self="handleClose">
    <div class="modal-content">
      <div class="modal-header">
        <h4>消息模板管理</h4>
        <button class="modal-close-btn" @click="handleClose">&times;</button>
      </div>
      <div class="modal-body">
        <div class="modal-split-layout">
          <!-- Left Panel - Template List -->
          <div class="modal-left-panel">
            <div class="modal-left-header">
              <h5>模板列表</h5>
              <button class="btn-sm btn-primary" @click="createNewTemplate">新建</button>
            </div>
            <div class="modal-left-content">
              <div class="template-list">
                <div 
                  v-for="template in templates" 
                  :key="template.id"
                  class="template-list-item"
                  :class="{ active: selectedTemplate?.id === template.id }"
                  @click="selectTemplate(template)"
                >
                  <div class="template-info">
                    <span class="template-name">{{ template.name }}</span>
                    <span class="template-topic">{{ template.topic }}</span>
                  </div>
                  <span class="template-actions">
                    <button title="复制" @click.stop="copyTemplate(template)">📋</button>
                    <button title="删除" @click.stop="deleteTemplate(template)">🗑</button>
                  </span>
                </div>
              </div>
            </div>
            <div class="modal-left-footer">
              <button class="btn-sm btn-primary" style="width: 100%;" @click="createNewTemplate">
                新建模板
              </button>
            </div>
          </div>

          <!-- Right Panel - Template Editor -->
          <div class="modal-right-panel">
            <div class="modal-right-header">
              <h5>模板详情</h5>
            </div>
            <div class="modal-right-content">
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="templateName" style="font-weight: 500; font-size: 14px;">模板名称</label>
                    <input
                      type="text"
                      id="templateName"
                      v-model="editingTemplate.name"
                      style="width: 100%;"
                      placeholder="输入模板名称"
                    />
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-group">
                    <label for="templateTopic" style="font-weight: 500; font-size: 14px;">默认Topic</label>
                    <input
                      type="text"
                      id="templateTopic"
                      v-model="editingTemplate.topic"
                      style="width: 100%;"
                      placeholder="例如: orders.new"
                    />
                  </div>
                </div>
              </div>
              <div class="form-group">
                <label for="templateCode">Python代码</label>
                <div class="code-editor-container">
                  <div class="code-editor-toolbar">
                    <div class="toolbar-left">
                      <button type="button" @click="formatCode" class="toolbar-btn">
                        🎨 格式化
                      </button>
                      <button type="button" @click="testCode" class="toolbar-btn">
                        ▶ 测试代码
                      </button>
                      <button type="button" @click="previewVariables" class="toolbar-btn">
                        👁 预览变量
                      </button>
                      <button type="button" @click="previewCode" class="toolbar-btn">
                        📋 预览
                      </button>
                    </div>
                    <div class="toolbar-right">
                      <label for="themeSelect" style="margin-right: 5px; font-size: 12px;">主题:</label>
                      <select id="themeSelect" v-model="selectedTheme" @change="changeTheme" class="theme-select">
                        <option value="default">默认</option>
                        <option value="dark">深色</option>
                        <option value="monokai">Monokai</option>
                      </select>
                    </div>
                  </div>
                  <div class="code-editor-wrapper">
                    <textarea
                      ref="codeEditor"
                      id="templateCodeEditor"
                      v-model="editingTemplate.code"
                      rows="15"
                      class="code-editor"
                      placeholder="def generate_message(device_id, cycle_count, message_index):&#10;    # 返回要发送的消息字典&#10;    return {&#10;        'device_id': device_id,&#10;        'timestamp': int(time.time()),&#10;        'data': 'your_data_here'&#10;    }"
                      @keydown="handleCodeKeydown"
                    ></textarea>
                    <div class="code-line-numbers" ref="lineNumbers"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn-secondary" @click="handleClose">取消</button>
        <button class="btn-primary" @click="saveTemplate">保存更改</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = ref(false)
const selectedTemplate = ref(null)
const codeEditor = ref(null)
const lineNumbers = ref(null)
const selectedTheme = ref('default')

// 模拟模板数据
const templates = ref([
  {
    id: 1,
    name: '订单创建模板',
    topic: 'orders.new',
    code: `import time
import random

def generate_message(device_id, cycle_count, message_index):
    return {
        'order_id': f'ORD-{device_id}-{cycle_count}-{message_index}',
        'user_id': f'user_{random.randint(1000, 9999)}',
        'product_id': f'prod_{random.randint(100, 999)}',
        'amount': round(random.uniform(10.0, 500.0), 2),
        'timestamp': int(time.time()),
        'status': 'created'
    }`
  },
  {
    id: 2,
    name: '支付成功模板',
    topic: 'payments.success',
    code: `import time
import random

def generate_message(device_id, cycle_count, message_index):
    return {
        'payment_id': f'PAY-{device_id}-{cycle_count}-{message_index}',
        'order_id': f'ORD-{random.randint(1000, 9999)}',
        'amount': round(random.uniform(10.0, 500.0), 2),
        'payment_method': random.choice(['credit_card', 'alipay', 'wechat']),
        'timestamp': int(time.time()),
        'status': 'success'
    }`
  },
  {
    id: 3,
    name: '库存更新模板',
    topic: 'inventory.update',
    code: `import time
import random

def generate_message(device_id, cycle_count, message_index):
    return {
        'product_id': f'prod_{random.randint(100, 999)}',
        'warehouse_id': f'wh_{device_id}',
        'quantity_change': random.randint(-50, 100),
        'current_stock': random.randint(0, 1000),
        'timestamp': int(time.time()),
        'reason': random.choice(['sale', 'restock', 'adjustment'])
    }`
  }
])

const editingTemplate = reactive({
  id: null,
  name: '',
  topic: '',
  code: ''
})

watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && templates.value.length > 0) {
    selectTemplate(templates.value[0])
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

watch(() => editingTemplate.code, () => {
  nextTick(() => {
    updateLineNumbers()
  })
})

const selectTemplate = (template) => {
  selectedTemplate.value = template
  Object.assign(editingTemplate, { ...template })
}

const createNewTemplate = () => {
  const newTemplate = {
    id: Date.now(),
    name: '新模板',
    topic: '',
    code: `import time

def generate_message(device_id, cycle_count, message_index):
    return {
        'device_id': device_id,
        'timestamp': int(time.time()),
        'cycle': cycle_count,
        'index': message_index
    }`
  }
  templates.value.push(newTemplate)
  selectTemplate(newTemplate)
}

const copyTemplate = (template) => {
  const copiedTemplate = {
    id: Date.now(),
    name: template.name + ' (副本)',
    topic: template.topic,
    code: template.code
  }
  templates.value.push(copiedTemplate)
  selectTemplate(copiedTemplate)
  ElMessage.success('模板复制成功')
}

const deleteTemplate = async (template) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板 "${template.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = templates.value.findIndex(t => t.id === template.id)
    if (index > -1) {
      templates.value.splice(index, 1)
      if (selectedTemplate.value?.id === template.id) {
        if (templates.value.length > 0) {
          selectTemplate(templates.value[0])
        } else {
          selectedTemplate.value = null
          Object.assign(editingTemplate, { id: null, name: '', topic: '', code: '' })
        }
      }
    }
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除模板失败:', error)
    }
  }
}

const testTemplate = () => {
  testCode()
}

// 新增的方法
const previewVariables = () => {
  ElMessage.info('预览变量功能：显示可用的环境变量')
  // TODO: 实现预览环境变量功能
}

const previewCode = () => {
  if (!editingTemplate.code.trim()) {
    ElMessage.warning('请先输入代码')
    return
  }

  ElMessage.info('代码预览功能：显示生成的消息示例')
  // TODO: 实现代码预览功能
}

const changeTheme = () => {
  // 应用主题到代码编辑器
  const editor = codeEditor.value
  if (editor) {
    editor.className = `code-editor theme-${selectedTheme.value}`
  }
  ElMessage.success(`已切换到 ${selectedTheme.value} 主题`)
}

// 代码编辑器相关方法
const formatCode = () => {
  if (!editingTemplate.code.trim()) {
    ElMessage.warning('请先输入代码')
    return
  }

  // 简单的代码格式化（实际项目中可以集成Python格式化工具）
  let formatted = editingTemplate.code
    .split('\n')
    .map(line => line.trim())
    .join('\n')
    .replace(/\n\n+/g, '\n\n') // 移除多余空行

  editingTemplate.code = formatted
  updateLineNumbers()
  ElMessage.success('代码格式化完成')
}

const testCode = () => {
  if (!editingTemplate.code.trim()) {
    ElMessage.error('请输入代码')
    return
  }

  // 简单的代码验证
  if (!editingTemplate.code.includes('def generate_message')) {
    ElMessage.error('代码必须包含 generate_message 函数')
    return
  }

  // 检查基本语法
  const lines = editingTemplate.code.split('\n')
  let indentLevel = 0
  let hasReturn = false

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    if (line.includes('return ')) {
      hasReturn = true
    }
  }

  if (!hasReturn) {
    ElMessage.warning('建议在函数中添加 return 语句')
  } else {
    ElMessage.success('代码语法检查通过！')
  }
}

const handleCodeKeydown = (event) => {
  // Tab键缩进
  if (event.key === 'Tab') {
    event.preventDefault()
    const start = event.target.selectionStart
    const end = event.target.selectionEnd
    const value = event.target.value

    event.target.value = value.substring(0, start) + '    ' + value.substring(end)
    event.target.selectionStart = event.target.selectionEnd = start + 4

    // 更新v-model
    editingTemplate.code = event.target.value
    updateLineNumbers()
  }
}

const updateLineNumbers = () => {
  if (!lineNumbers.value || !editingTemplate.code) return

  const lines = editingTemplate.code.split('\n')
  const lineNumbersHtml = lines.map((_, index) =>
    `<div class="line-number">${index + 1}</div>`
  ).join('')

  lineNumbers.value.innerHTML = lineNumbersHtml
}

const saveTemplate = () => {
  if (!editingTemplate.name.trim()) {
    ElMessage.error('请输入模板名称')
    return
  }
  
  if (!editingTemplate.topic.trim()) {
    ElMessage.error('请输入Topic')
    return
  }
  
  if (!editingTemplate.code.trim()) {
    ElMessage.error('请输入代码')
    return
  }
  
  if (editingTemplate.id) {
    // 更新现有模板
    const index = templates.value.findIndex(t => t.id === editingTemplate.id)
    if (index > -1) {
      templates.value[index] = { ...editingTemplate }
      selectedTemplate.value = templates.value[index]
    }
  } else {
    // 创建新模板
    const newTemplate = { ...editingTemplate, id: Date.now() }
    templates.value.push(newTemplate)
    selectedTemplate.value = newTemplate
  }
  
  ElMessage.success('保存成功')
}

const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
/* 继承环境变量弹窗的基础样式 */
.modal {
  display: flex;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.4);
  align-items: center;
  justify-content: center;
}

.modal-content {
  background-color: #fefefe;
  border: 1px solid #888;
  width: 85%;
  max-width: 1200px;
  border-radius: 5px;
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
  display: flex;
  flex-direction: column;
  max-height: 85vh;
}

.modal-header {
  padding: 15px 20px;
  background-color: #2c3e50;
  color: #ecf0f1;
  border-bottom: 1px solid #34495e;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.modal-header h4 {
  margin: 0;
  font-size: 1.2em;
}

.modal-close-btn {
  color: #aaa;
  font-size: 28px;
  font-weight: bold;
  background: none;
  border: none;
  cursor: pointer;
}

.modal-close-btn:hover,
.modal-close-btn:focus {
  color: #ecf0f1;
  text-decoration: none;
}

.modal-body {
  padding: 20px;
  flex-grow: 1;
  overflow: hidden; /* 不显示滚动条 */
  min-height: 400px; /* 减少最小高度 */
}

.modal-footer {
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-top: 1px solid #e0e0e0;
  text-align: right;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.modal-footer button {
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 10px;
  border: 1px solid;
}

.btn-primary {
  background-color: #3498db;
  color: white;
  border-color: #3498db;
}

.btn-secondary {
  background-color: #e0e0e0;
  color: #333;
  border-color: #ccc;
}

.btn-success {
  background-color: #27ae60;
  color: white;
  border-color: #27ae60;
}

/* 分割布局 */
.modal-split-layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 20px;
  height: 500px; /* 减少高度 */
}

.modal-left-panel {
  border-right: 1px solid #e0e0e0;
  padding-right: 15px;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 重要：允许flex子项收缩 */
}

.modal-left-header {
  flex-shrink: 0; /* 头部不收缩 */
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-left-header h5 {
  margin: 0;
}

.btn-sm {
  padding: 4px 8px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.8em;
}

.btn-sm.btn-primary {
  background: #3498db;
  color: white;
}

.btn-sm.btn-secondary {
  background: #f0f0f0;
  border: 1px solid #ddd;
  color: #333;
}

.modal-left-content {
  flex: 1;
  min-height: 0; /* 重要：允许内容区域收缩 */
  overflow: hidden;
}

.template-list {
  border: 1px solid #ddd;
  border-radius: 4px;
  height: 100%;
  overflow-y: auto;
}

.template-list-item {
  padding: 12px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s;
}

.template-list-item:hover {
  background-color: #f5f5f5;
}

.template-list-item.active {
  background-color: #e3f2fd;
  border-left: 3px solid #3498db;
}

.template-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.template-name {
  font-weight: 500;
  font-size: 0.95em;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.template-topic {
  font-size: 0.8em;
  color: #666;
  font-family: monospace;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.template-actions button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.9em;
  margin-left: 5px;
  padding: 2px 4px;
}

.template-actions button:hover {
  background-color: rgba(0,0,0,0.1);
  border-radius: 2px;
}

.modal-left-footer {
  flex-shrink: 0; /* 底部不收缩 */
  margin-top: 15px;
}

.modal-right-panel {
  display: flex;
  flex-direction: column;
  min-height: 0; /* 重要：允许flex子项收缩 */
}

.modal-right-header {
  flex-shrink: 0; /* 头部不收缩 */
}

.modal-right-header h5 {
  margin-top: 0;
}

.modal-right-content {
  flex: 1;
  min-height: 0; /* 重要：允许内容区域收缩 */
  overflow-y: auto;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  font-size: 0.9em;
}

.form-group input,
.form-group textarea {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 0.95em;
}

.form-group textarea {
  resize: vertical;
}

.code-editor-container {
  border: 1px solid #ddd;
  border-radius: 6px;
  background-color: #f8f9fa;
  overflow: hidden;
  max-height: 400px; /* 限制代码编辑器最大高度 */
  display: flex;
  flex-direction: column;
}

.code-editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #e9ecef;
  border-bottom: 1px solid #ddd;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-btn {
  padding: 4px 8px;
  border: 1px solid #ccc;
  border-radius: 3px;
  background: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.toolbar-btn:hover {
  background-color: #f0f0f0;
  border-color: #999;
}

.theme-select {
  padding: 2px 6px;
  border: 1px solid #ccc;
  border-radius: 3px;
  font-size: 12px;
  background: white;
}

/* 两列布局 */
.row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.col-md-6 {
  flex: 1;
}

/* 主题样式 */
.code-editor.theme-default {
  background: #fff;
  color: #333;
}

.code-editor.theme-dark {
  background: #2d3748;
  color: #e2e8f0;
}

.code-editor.theme-monokai {
  background: #272822;
  color: #f8f8f2;
}

.code-editor-wrapper {
  position: relative;
  display: flex;
  background: #fff;
  flex: 1;
  min-height: 0; /* 允许收缩 */
  overflow: hidden;
}

.code-line-numbers {
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  padding: 8px 8px 8px 12px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #666;
  user-select: none;
  min-width: 40px;
  text-align: right;
}

.line-number {
  height: 21px; /* 匹配textarea行高 */
}

.code-editor {
  flex: 1;
  border: none;
  outline: none;
  padding: 8px 12px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  background: transparent;
  resize: none;
  tab-size: 4;
  white-space: pre;
  overflow-wrap: normal;
  overflow: auto; /* 允许滚动 */
  min-height: 300px; /* 最小高度 */
  max-height: 350px; /* 最大高度 */
}

.code-help {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  font-size: 0.85em;
}

.code-help p {
  margin: 0 0 8px 0;
}

.code-help ul {
  margin: 8px 0;
  padding-left: 20px;
}

.code-help li {
  margin-bottom: 4px;
}

.code-help code {
  background-color: #e9ecef;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}
</style>
