<template>
  <div class="api-test">
    <h2>API连接测试</h2>
    
    <div class="test-section">
      <h3>任务API测试</h3>
      <button @click="testTaskList" class="test-btn">测试任务列表</button>
      <button @click="testRunningTasks" class="test-btn">测试运行中任务</button>
      <pre v-if="taskResult">{{ taskResult }}</pre>
    </div>

    <div class="test-section">
      <h3>环境API测试</h3>
      <button @click="testEnvList" class="test-btn">测试环境列表</button>
      <pre v-if="envResult">{{ envResult }}</pre>
    </div>

    <div class="test-section">
      <h3>模板API测试</h3>
      <button @click="testTemplateList" class="test-btn">测试模板列表</button>
      <pre v-if="templateResult">{{ templateResult }}</pre>
    </div>

    <div class="test-section">
      <h3>WebSocket测试</h3>
      <button @click="testWebSocket" class="test-btn">测试WebSocket连接</button>
      <div v-if="wsStatus" :class="['ws-status', wsStatus.type]">
        {{ wsStatus.message }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import api from '@/api'
import { io } from 'socket.io-client'

const taskResult = ref('')
const envResult = ref('')
const templateResult = ref('')
const wsStatus = ref(null)

const testTaskList = async () => {
  try {
    const result = await api.get('/task/api/list')
    taskResult.value = JSON.stringify(result, null, 2)
  } catch (error) {
    taskResult.value = `错误: ${error.message}`
  }
}

const testRunningTasks = async () => {
  try {
    const result = await api.get('/task/api/running_tasks')
    taskResult.value = JSON.stringify(result, null, 2)
  } catch (error) {
    taskResult.value = `错误: ${error.message}`
  }
}

const testEnvList = async () => {
  try {
    const result = await api.get('/env/api/list')
    envResult.value = JSON.stringify(result, null, 2)
  } catch (error) {
    envResult.value = `错误: ${error.message}`
  }
}

const testTemplateList = async () => {
  try {
    const result = await api.get('/template/api/list')
    templateResult.value = JSON.stringify(result, null, 2)
  } catch (error) {
    templateResult.value = `错误: ${error.message}`
  }
}

const testWebSocket = () => {
  try {
    const socket = io('http://localhost:5000')
    
    socket.on('connect', () => {
      wsStatus.value = { type: 'success', message: 'WebSocket连接成功！' }
    })
    
    socket.on('disconnect', () => {
      wsStatus.value = { type: 'warning', message: 'WebSocket连接断开' }
    })
    
    socket.on('connect_error', (error) => {
      wsStatus.value = { type: 'error', message: `WebSocket连接失败: ${error.message}` }
    })
    
    // 5秒后断开连接
    setTimeout(() => {
      socket.disconnect()
    }, 5000)
    
  } catch (error) {
    wsStatus.value = { type: 'error', message: `WebSocket测试失败: ${error.message}` }
  }
}
</script>

<style scoped>
.api-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.test-btn {
  margin-right: 10px;
  margin-bottom: 10px;
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.test-btn:hover {
  background: #66b1ff;
}

pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 300px;
  margin-top: 10px;
}

.ws-status {
  padding: 10px;
  border-radius: 4px;
  margin-top: 10px;
}

.ws-status.success {
  background: #f0f9ff;
  color: #059669;
  border: 1px solid #a7f3d0;
}

.ws-status.warning {
  background: #fffbeb;
  color: #d97706;
  border: 1px solid #fde68a;
}

.ws-status.error {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}
</style>
