<template>
  <el-container class="layout-container">
    <!-- 侧边栏 -->
    <el-aside width="280px" class="sidebar">
      <div class="sidebar-header">
        <h2>KafkaTool</h2>
      </div>

      <!-- 新建任务按钮 -->
      <button
        class="sidebar-new-task-btn"
        @click="showNewTaskDialog = true"
      >
        ＋ 新建任务
      </button>

      <!-- 我的任务区域 -->
      <div class="sidebar-section-title">我的任务</div>
      <ul class="task-list" id="taskList">
        <li v-for="task in recentTasks" :key="task.id"
            :class="{ active: selectedTaskId === task.id }"
            @click="selectTask(task)">
          <span class="task-name" :title="task.name">{{ task.name }}</span>
          <span class="task-actions">
            <button title="运行" @click.stop="runTask(task)">▶</button>
            <button title="复制" @click.stop="copyTask(task)">❏</button>
            <button title="删除" @click.stop="deleteTask(task)">🗑</button>
          </span>
        </li>
      </ul>

      <!-- 管理区域 -->
      <div class="sidebar-section-title">管理</div>
      <ul class="management-list">
        <li @click="$router.push('/environments')"
            :class="{ active: $route.path === '/environments' }">
          <span class="management-item-name">
            <i class="icon-globe">🌐</i> 环境变量
          </span>
        </li>
        <li @click="$router.push('/templates')"
            :class="{ active: $route.path === '/templates' }">
          <span class="management-item-name">
            <i class="icon-template">📄</i> 消息模板
          </span>
        </li>
      </ul>
    </el-aside>

    <!-- 主内容区 -->
    <el-container class="main-content">
      <!-- 顶部导航 -->
      <el-header class="main-header">
        <input
          type="text"
          v-model="currentTaskName"
          placeholder="任务名称"
          class="task-name-input"
        />
        <button class="run-button" @click="runCurrentTask">
          ▶ 运行
        </button>
      </el-header>

      <!-- 主内容 -->
      <el-main class="main-tabs-container">
        <!-- 标签页导航 -->
        <div class="tabs">
          <button
            v-for="tab in tabs"
            :key="tab.key"
            class="tab-button"
            :class="{ active: activeTab === tab.key }"
            @click="activeTab = tab.key"
          >
            {{ tab.label }}
          </button>
        </div>

        <!-- 标签页内容 -->
        <div class="tab-content-wrapper">
          <router-view v-if="activeTab === 'config'" />
          <div v-else-if="activeTab === 'output'" class="tab-content active">
            <div class="log-controls">
              <button>清除日志</button>
              <div>
                <input type="checkbox" id="autoScrollLog" checked>
                <label for="autoScrollLog">自动滚动</label>
              </div>
              <input type="text" placeholder="筛选日志..." class="log-filter">
            </div>
            <div class="status-bar">
              <span>状态: <span class="status-code success">就绪</span></span>
              <span>消息数: <span>0</span></span>
              <span>周期数: <span>0</span></span>
              <span>错误数: <span class="status-code">0</span></span>
              <span>已运行时长: <span>00:00:00</span></span>
            </div>
            <div class="response-data">
              <span class="log-info">欢迎使用 KafkaTool！</span>
              <span class="log-info">请配置任务参数并点击运行按钮开始测试。</span>
            </div>
          </div>
          <div v-else-if="activeTab === 'history'" class="tab-content active">
            <div style="padding: 20px;">
              <p>运行历史内容</p>
            </div>
          </div>
          <div v-else-if="activeTab === 'charts'" class="tab-content active">
            <div style="padding: 20px;">
              <p>统计图表内容</p>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>

    <!-- 新建任务对话框 -->
    <NewTaskDialog v-model="showNewTaskDialog" />
  </el-container>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import NewTaskDialog from '@/components/NewTaskDialog.vue'

const router = useRouter()
const showNewTaskDialog = ref(false)
const selectedTaskId = ref(null)
const currentTaskName = ref('')
const activeTab = ref('config')

// 模拟任务数据
const recentTasks = ref([
  { id: 1, name: '订单处理模拟' },
  { id: 2, name: '日志收集测试' },
  { id: 3, name: '用户行为分析' }
])

// 标签页配置
const tabs = ref([
  { key: 'config', label: '任务配置' },
  { key: 'output', label: '运行输出' },
  { key: 'history', label: '运行历史' },
  { key: 'charts', label: '统计图表' }
])

// 选择任务
const selectTask = (task) => {
  selectedTaskId.value = task.id
  currentTaskName.value = task.name
  // 如果当前不在任务管理页面，跳转到任务管理页面
  if (router.currentRoute.value.path !== '/tasks') {
    router.push('/tasks')
  }
}

// 运行任务
const runTask = (task) => {
  console.log('运行任务:', task.name)
  // TODO: 实现运行任务逻辑
}

// 复制任务
const copyTask = (task) => {
  console.log('复制任务:', task.name)
  // TODO: 实现复制任务逻辑
}

// 删除任务
const deleteTask = (task) => {
  console.log('删除任务:', task.name)
  // TODO: 实现删除任务逻辑
}

// 运行当前任务
const runCurrentTask = () => {
  console.log('运行当前任务:', currentTaskName.value)
  // TODO: 实现运行当前任务逻辑
}

onMounted(() => {
  // 默认选择第一个任务
  if (recentTasks.value.length > 0) {
    selectTask(recentTasks.value[0])
  }
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
  display: flex;
}

.sidebar {
  width: 280px;
  background-color: #2c3e50;
  color: #ecf0f1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #34495e;
}

.sidebar-header {
  padding: 10px 0;
  margin-bottom: 20px;
  border-bottom: 1px solid #34495e;
}

.sidebar-header h2 {
  margin: 0;
  font-size: 1.4em;
  color: #ecf0f1;
}

.sidebar-new-task-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 10px 15px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 1em;
  margin-bottom: 15px;
  cursor: pointer;
  border-radius: 4px;
  width: 100%;
}

.sidebar-new-task-btn:hover {
  background-color: #2980b9;
}

.sidebar-section-title {
  font-weight: bold;
  margin-top: 20px;
  margin-bottom: 10px;
  color: #bdc3c7;
  font-size: 0.9em;
  text-transform: uppercase;
}

.task-list {
  list-style: none;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  flex-grow: 1;
}

.task-list li {
  padding: 10px 15px;
  cursor: pointer;
  border-bottom: 1px solid #34495e;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.15s ease;
  position: relative;
  min-height: 44px;
}

.task-list li:hover {
  background-color: #34495e;
}

.task-list li.active {
  background-color: #3498db;
  color: #ffffff;
  border-left: 4px solid #2980b9;
}

.task-list li.active .task-name {
  font-weight: 600;
}

.task-name {
  flex: 1;
  margin-right: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
  font-size: 0.9em;
  line-height: 1.4;
}

.task-actions {
  display: flex;
  gap: 5px;
  flex-shrink: 0;
}

.task-list li .task-actions button {
  background: none;
  border: none;
  color: #95a5a6;
  cursor: pointer;
  padding: 3px;
  font-size: 0.9em;
}

.task-list li .task-actions button:hover {
  color: #ecf0f1;
}

.management-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.management-list li {
  padding: 10px 15px;
  cursor: pointer;
  border-bottom: 1px solid #34495e;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
  font-size: 0.95em;
}

.management-list li:hover,
.management-list li.active {
  background-color: #34495e;
}

.management-list li .management-item-name {
  display: flex;
  align-items: center;
}

.management-list li i {
  margin-right: 8px;
  width: 16px;
  text-align: center;
}

.main-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.main-header {
  padding: 10px 20px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
}

.task-name-input {
  flex-grow: 1;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1em;
  margin-right: 10px;
}

.run-button {
  background-color: #27ae60;
  color: white;
  border: none;
  padding: 8px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 1em;
  cursor: pointer;
  border-radius: 4px;
}

.run-button:hover {
  background-color: #229954;
}

.main-tabs-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  padding: 0;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  padding: 0 20px;
  background-color: #f8f9fa;
  align-items: center;
}

.tab-button {
  padding: 12px 20px;
  cursor: pointer;
  border: none;
  background: none;
  font-size: 0.95em;
  border-bottom: 3px solid transparent;
  margin-bottom: -1px;
  transition: all 0.2s ease;
}

.tab-button.active {
  border-bottom: 3px solid #3498db;
  color: #3498db;
  font-weight: bold;
  background-color: #ffffff;
}

.tab-button:hover {
  background-color: #e9ecef;
}

.tab-content-wrapper {
  flex-grow: 1;
  overflow-y: auto;
}

.tab-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.log-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  gap: 15px;
}

.log-controls button {
  padding: 6px 12px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
}

.log-controls button:hover {
  background-color: #5a6268;
}

.log-controls div {
  display: flex;
  align-items: center;
  gap: 8px;
}

.log-controls input[type="checkbox"] {
  margin: 0;
}

.log-controls label {
  margin: 0;
  font-size: 0.9em;
  color: #666;
}

.log-filter {
  padding: 6px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 0.9em;
}

.status-bar {
  margin-bottom: 15px;
  padding: 15px 20px;
  background-color: #e9ecef;
  border-radius: 4px;
  font-size: 0.9em;
}

.status-bar span {
  margin-right: 15px;
}

.status-bar .status-code.success {
  color: #27ae60;
  font-weight: bold;
}

.status-bar .status-code.error {
  color: #c0392b;
  font-weight: bold;
}

.response-data {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  min-height: 400px;
  max-height: 600px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  margin: 0 20px 20px 20px;
  line-height: 1.4;
  display: flex;
  flex-direction: column;
}

.response-data .log-info {
  color: #3498db;
  display: block;
  margin-bottom: 5px;
}

.response-data .log-warning {
  color: #f39c12;
  display: block;
  margin-bottom: 5px;
}

.response-data .log-error {
  color: #e74c3c;
  display: block;
  margin-bottom: 5px;
}

.response-data .log-success {
  color: #27ae60;
  display: block;
  margin-bottom: 5px;
}
</style>
