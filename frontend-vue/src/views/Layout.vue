<template>
  <el-container class="layout-container">
    <!-- 侧边栏 -->
    <el-aside width="280px" class="sidebar">
      <div class="sidebar-header">
        <h2>KafkaTool</h2>
      </div>

      <!-- 新建任务按钮 -->
      <button
        class="sidebar-new-task-btn"
        @click="showNewTaskDialog = true"
      >
        ＋ 新建任务
      </button>

      <!-- 我的任务区域 -->
      <div class="sidebar-section-title">我的任务</div>
      <ul class="task-list" id="taskList">
        <li v-for="task in recentTasks" :key="task.id"
            :class="{ active: selectedTaskId === task.id }"
            @click="selectTask(task)">
          <span class="task-name" :title="task.name">{{ task.name }}</span>
          <span class="task-actions">
            <button title="运行" @click.stop="runTask(task)">▶</button>
            <button title="复制" @click.stop="copyTask(task)">❏</button>
            <button title="删除" @click.stop="deleteTask(task)">🗑</button>
          </span>
        </li>
      </ul>

      <!-- 管理区域 -->
      <div class="sidebar-section-title">管理</div>
      <ul class="management-list">
        <li @click="showEnvManagementModal = true"
            :class="{ active: showEnvManagementModal }">
          <span class="management-item-name">
            <i class="icon-globe">🌐</i> 环境变量
          </span>
        </li>
        <li @click="showTemplateManagementModal = true"
            :class="{ active: showTemplateManagementModal }">
          <span class="management-item-name">
            <i class="icon-template">📄</i> 消息模板
          </span>
        </li>
      </ul>
    </el-aside>

    <!-- 主内容区 -->
    <el-container class="main-content">
      <transition name="fade" mode="out-in">
        <div :key="selectedTaskId" class="main-content-inner">
          <!-- 顶部导航 -->
          <el-header class="main-header">
            <input
              type="text"
              v-model="currentTaskName"
              placeholder="任务名称"
              class="task-name-input"
            />
            <button class="run-button" @click="runCurrentTask">
              ▶ 运行
            </button>
          </el-header>

          <!-- 主内容 -->
          <el-main class="main-tabs-container">
        <!-- 标签页导航 -->
        <div class="tabs">
          <button
            v-for="tab in tabs"
            :key="tab.key"
            class="tab-button"
            :class="{ active: activeTab === tab.key }"
            @click="activeTab = tab.key"
          >
            {{ tab.label }}
          </button>
        </div>

            <!-- 标签页内容 -->
            <div class="tab-content-wrapper">
              <router-view v-if="activeTab === 'config'" />
              <div v-else-if="activeTab === 'output'" class="tab-content active">
              <div class="log-controls">
              <div style="display: flex; align-items: center; gap: 8px;">
                <label for="maxLogLinesInput" style="font-size: 14px; font-weight: 500; margin: 0;">最大日志条数:</label>
                <input
                  type="number"
                  id="maxLogLinesInput"
                  v-model="logSettings.maxLines"
                  min="5"
                  max="1000"
                  style="width: 80px; padding: 4px 8px; border: 1px solid #ccc; border-radius: 4px; font-size: 14px;"
                />
                <button
                  @click="applyLogSettings"
                  style="padding: 4px 12px; font-size: 13px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;"
                >
                  ✓ 应用
                </button>
              </div>
              <button @click="clearLogs">清除日志</button>
              <div>
                <input type="checkbox" id="autoScrollLog" v-model="logSettings.autoScroll">
                <label for="autoScrollLog">自动滚动</label>
              </div>
              <input
                type="text"
                v-model="logSettings.filter"
                placeholder="筛选日志..."
                class="log-filter"
              />
            </div>
            <div class="status-bar">
              <span>状态: <span :class="['status-code', runStatus.type]">{{ runStatus.text }}</span></span>
              <span>消息数: <span>{{ runStats.messageCount }}</span></span>
              <span>周期数: <span>{{ runStats.cycleCount }}</span></span>
              <span>错误数: <span class="status-code">{{ runStats.errorCount }}</span></span>
              <span>已运行时长: <span>{{ runStats.elapsedTime }}</span></span>
            </div>
            <div class="response-data" ref="logContainer">
              <div
                v-for="(log, index) in filteredLogs"
                :key="index"
                :class="['log-entry', `log-${log.type}`]"
              >
                <span class="log-timestamp">{{ log.timestamp }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
              <div v-if="logs.length === 0" class="log-empty">
                <span class="log-info">欢迎使用 KafkaTool！</span>
                <span class="log-info">请配置任务参数并点击运行按钮开始测试。</span>
              </div>
            </div>
              </div>
              <div v-else-if="activeTab === 'history'" class="tab-content active">
                <div style="padding: 20px;">
                  <div class="history-controls">
                <button @click="refreshHistory" class="btn-primary">
                  🔄 刷新历史
                </button>
                <input
                  type="text"
                  v-model="historyFilter"
                  placeholder="筛选历史记录..."
                  class="history-filter"
                />
                <button @click="clearTaskHistory" class="btn-danger">
                  🗑 清空当前任务历史
                </button>
                <span class="history-summary">{{ historySummary }}</span>
              </div>

              <div class="history-items-container">
                <div v-if="filteredHistory.length === 0" class="history-empty-state">
                  <div class="empty-icon">🕒</div>
                  <h5>暂无运行历史记录</h5>
                  <p>运行任务后，历史记录将显示在这里</p>
                </div>
                <div
                  v-for="item in filteredHistory"
                  :key="item.id"
                  class="history-item"
                  @click="viewHistoryDetail(item)"
                >
                  <div class="history-item-header">
                    <span class="history-task-name">{{ item.taskName }}</span>
                    <span :class="['history-status', `status-${item.status}`]">{{ getStatusText(item.status) }}</span>
                  </div>
                  <div class="history-item-details">
                    <span>开始时间: {{ item.startTime }}</span>
                    <span>运行时长: {{ item.duration }}</span>
                    <span>消息数: {{ item.messageCount }}</span>
                    <span>错误数: {{ item.errorCount }}</span>
                  </div>
                </div>
                  </div>
                </div>
              </div>
              <div v-else-if="activeTab === 'charts'" class="tab-content active">
                <div style="padding: 20px;">
                  <div class="charts-header">
                <h4 style="margin: 0;">统计图表</h4>
                <div style="display: flex; gap: 10px; align-items: center;">
                  <span class="chart-task-name">{{ currentTaskName || '未选择任务' }}</span>
                  <button @click="refreshCharts" class="btn-primary">
                    🔄 刷新图表
                  </button>
                </div>
              </div>

              <div class="charts-container">
                <!-- 图表控制区域 -->
                <div class="charts-controls">
                  <div>
                    <h5 style="margin: 0 0 8px 0; color: #2c3e50;">最后一次运行统计</h5>
                    <div class="charts-summary">{{ chartsSummary }}</div>
                  </div>
                  <div class="charts-stats">
                    <div class="stat-item">
                      <div class="stat-label">运行状态</div>
                      <div class="stat-value">{{ chartStats.status }}</div>
                    </div>
                    <div class="stat-item">
                      <div class="stat-label">总消息数</div>
                      <div class="stat-value">{{ chartStats.totalMessages }}</div>
                    </div>
                    <div class="stat-item">
                      <div class="stat-label">运行时长</div>
                      <div class="stat-value">{{ chartStats.duration }}</div>
                    </div>
                  </div>
                </div>

                <!-- 图表区域 -->
                <div v-if="chartStats.totalMessages > 0" class="charts-grid">
                  <!-- 消息发送趋势图 -->
                  <div class="chart-card">
                    <h6>消息发送趋势</h6>
                    <div class="chart-placeholder">
                      <canvas ref="messagesTrendChart" width="400" height="300"></canvas>
                    </div>
                  </div>

                  <!-- 队列分布图 -->
                  <div class="chart-card">
                    <h6>队列消息分布</h6>
                    <div class="chart-placeholder">
                      <canvas ref="queueDistributionChart" width="400" height="300"></canvas>
                    </div>
                  </div>
                </div>

                <!-- 空状态 -->
                <div v-else class="charts-empty-state">
                  <div class="empty-icon">📊</div>
                  <h5>暂无统计数据</h5>
                  <p>运行任务后，统计图表将显示在这里</p>
                </div>
                  </div>
                </div>
              </div>
            </div>
          </el-main>
        </div>
      </transition>
    </el-container>

    <!-- 新建任务对话框 -->
    <NewTaskDialog v-model="showNewTaskDialog" />

    <!-- 环境变量管理弹窗 -->
    <EnvironmentManagementModal v-model="showEnvManagementModal" />

    <!-- 消息模板管理弹窗 -->
    <TemplateManagementModal v-model="showTemplateManagementModal" />
  </el-container>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import NewTaskDialog from '@/components/NewTaskDialog.vue'
import EnvironmentManagementModal from '@/components/EnvironmentManagementModal.vue'
import TemplateManagementModal from '@/components/TemplateManagementModal.vue'

const router = useRouter()
const showNewTaskDialog = ref(false)
const showEnvManagementModal = ref(false)
const showTemplateManagementModal = ref(false)
const selectedTaskId = ref(null)
const currentTaskName = ref('')
const activeTab = ref('config')

// 日志相关数据
const logs = ref([])
const logContainer = ref(null)
const logSettings = reactive({
  maxLines: 100,
  autoScroll: true,
  filter: ''
})

// 运行状态数据
const runStatus = reactive({
  type: 'success',
  text: '就绪'
})

const runStats = reactive({
  messageCount: 0,
  cycleCount: 0,
  errorCount: 0,
  elapsedTime: '00:00:00'
})

// 历史记录数据
const historyFilter = ref('')
const runHistory = ref([
  {
    id: 1,
    taskName: '订单处理模拟',
    status: 'completed',
    startTime: '2024-01-15 14:30:25',
    duration: '05:23',
    messageCount: 1250,
    errorCount: 0
  },
  {
    id: 2,
    taskName: '日志收集测试',
    status: 'failed',
    startTime: '2024-01-15 13:15:10',
    duration: '02:45',
    messageCount: 680,
    errorCount: 15
  },
  {
    id: 3,
    taskName: '用户行为分析',
    status: 'completed',
    startTime: '2024-01-15 11:45:30',
    duration: '10:15',
    messageCount: 2340,
    errorCount: 2
  }
])

// 图表数据
const chartStats = reactive({
  status: '-',
  totalMessages: 0,
  duration: '-'
})

const chartsSummary = ref('正在加载统计数据...')
const messagesTrendChart = ref(null)
const queueDistributionChart = ref(null)

// 模拟任务数据
const recentTasks = ref([
  { id: 1, name: '订单处理模拟' },
  { id: 2, name: '日志收集测试' },
  { id: 3, name: '用户行为分析' }
])

// 标签页配置
const tabs = ref([
  { key: 'config', label: '任务配置' },
  { key: 'output', label: '运行输出' },
  { key: 'history', label: '运行历史' },
  { key: 'charts', label: '统计图表' }
])

// 计算属性
const filteredLogs = computed(() => {
  if (!logSettings.filter) return logs.value.slice(-logSettings.maxLines)

  return logs.value
    .filter(log => log.message.toLowerCase().includes(logSettings.filter.toLowerCase()))
    .slice(-logSettings.maxLines)
})

const filteredHistory = computed(() => {
  if (!historyFilter.value) return runHistory.value

  return runHistory.value.filter(item =>
    item.taskName.toLowerCase().includes(historyFilter.value.toLowerCase()) ||
    item.status.toLowerCase().includes(historyFilter.value.toLowerCase())
  )
})

const historySummary = computed(() => {
  const total = runHistory.value.length
  const completed = runHistory.value.filter(item => item.status === 'completed').length
  const failed = runHistory.value.filter(item => item.status === 'failed').length

  return `共 ${total} 条记录，成功 ${completed} 条，失败 ${failed} 条`
})

// 选择任务
const selectTask = (task) => {
  selectedTaskId.value = task.id
  currentTaskName.value = task.name
  // 如果当前不在任务管理页面，跳转到任务管理页面
  if (router.currentRoute.value.path !== '/tasks') {
    router.push('/tasks')
  }
}

// 运行任务
const runTask = (task) => {
  console.log('运行任务:', task.name)
  // TODO: 实现运行任务逻辑
}

// 复制任务
const copyTask = (task) => {
  console.log('复制任务:', task.name)
  // TODO: 实现复制任务逻辑
}

// 删除任务
const deleteTask = (task) => {
  console.log('删除任务:', task.name)
  // TODO: 实现删除任务逻辑
}

// 运行当前任务
const runCurrentTask = () => {
  console.log('运行当前任务:', currentTaskName.value)
  // 模拟开始运行
  runStatus.type = 'warning'
  runStatus.text = '运行中'

  // 添加开始日志
  addLog('info', '任务开始运行...')
  addLog('info', `任务名称: ${currentTaskName.value}`)

  // 模拟运行过程
  simulateTaskRun()
}

// 日志相关方法
const addLog = (type, message) => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.push({
    type,
    message,
    timestamp
  })

  // 自动滚动到底部
  if (logSettings.autoScroll) {
    nextTick(() => {
      if (logContainer.value) {
        logContainer.value.scrollTop = logContainer.value.scrollHeight
      }
    })
  }
}

const clearLogs = () => {
  logs.value = []
  addLog('info', '日志已清除')
}

const applyLogSettings = () => {
  addLog('info', `日志设置已更新: 最大条数 ${logSettings.maxLines}`)
}

// 历史记录方法
const refreshHistory = () => {
  addLog('info', '历史记录已刷新')
}

const clearTaskHistory = () => {
  runHistory.value = []
  addLog('info', '当前任务历史已清空')
}

const viewHistoryDetail = (item) => {
  console.log('查看历史详情:', item)
  addLog('info', `查看历史记录: ${item.taskName}`)
}

const getStatusText = (status) => {
  const statusMap = {
    'completed': '已完成',
    'running': '运行中',
    'failed': '失败',
    'stopped': '已停止'
  }
  return statusMap[status] || status
}

// 图表方法
const refreshCharts = () => {
  addLog('info', '图表数据已刷新')
  // 模拟更新图表数据
  chartStats.status = '已完成'
  chartStats.totalMessages = 1250
  chartStats.duration = '05:23'
  chartsSummary.value = '最后运行于 2024-01-15 14:30:25，共发送 1250 条消息'
}

// 模拟任务运行
const simulateTaskRun = () => {
  let messageCount = 0
  let cycleCount = 0
  let seconds = 0

  const interval = setInterval(() => {
    seconds++
    cycleCount = Math.floor(seconds / 10) + 1
    messageCount += Math.floor(Math.random() * 5) + 1

    // 更新统计数据
    runStats.messageCount = messageCount
    runStats.cycleCount = cycleCount
    runStats.elapsedTime = formatTime(seconds)

    // 添加随机日志
    if (seconds % 5 === 0) {
      const logTypes = ['info', 'success', 'warning']
      const logType = logTypes[Math.floor(Math.random() * logTypes.length)]
      addLog(logType, `周期 ${cycleCount}: 发送了 ${Math.floor(Math.random() * 10) + 1} 条消息`)
    }

    // 模拟运行60秒后完成
    if (seconds >= 60) {
      clearInterval(interval)
      runStatus.type = 'success'
      runStatus.text = '已完成'
      addLog('success', '任务运行完成！')

      // 添加到历史记录
      runHistory.value.unshift({
        id: Date.now(),
        taskName: currentTaskName.value,
        status: 'completed',
        startTime: new Date().toLocaleString(),
        duration: formatTime(seconds),
        messageCount: messageCount,
        errorCount: runStats.errorCount
      })
    }
  }, 1000)
}

const formatTime = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

onMounted(() => {
  // 默认选择第一个任务
  if (recentTasks.value.length > 0) {
    selectTask(recentTasks.value[0])
  }

  // 添加欢迎日志
  addLog('info', '欢迎使用 KafkaTool！')
  addLog('info', '请配置任务参数并点击运行按钮开始测试。')
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
  display: flex;
}

.sidebar {
  width: 280px;
  background-color: #2c3e50;
  color: #ecf0f1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #34495e;
}

.sidebar-header {
  padding: 10px 0;
  margin-bottom: 20px;
  border-bottom: 1px solid #34495e;
}

.sidebar-header h2 {
  margin: 0;
  font-size: 1.4em;
  color: #ecf0f1;
}

.sidebar-new-task-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 10px 15px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 1em;
  margin-bottom: 15px;
  cursor: pointer;
  border-radius: 4px;
  width: 100%;
}

.sidebar-new-task-btn:hover {
  background-color: #2980b9;
}

.sidebar-section-title {
  font-weight: bold;
  margin-top: 20px;
  margin-bottom: 10px;
  color: #bdc3c7;
  font-size: 0.9em;
  text-transform: uppercase;
}

.task-list {
  list-style: none;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  flex-grow: 1;
}

.task-list li {
  padding: 10px 15px;
  cursor: pointer;
  border-bottom: 1px solid #34495e;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.15s ease;
  position: relative;
  min-height: 44px;
}

.task-list li:hover {
  background-color: #34495e;
}

.task-list li.active {
  background-color: #3498db;
  color: #ffffff;
  border-left: 4px solid #2980b9;
}

.task-list li.active .task-name {
  font-weight: 600;
}

.task-name {
  flex: 1;
  margin-right: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
  font-size: 0.9em;
  line-height: 1.4;
}

.task-actions {
  display: flex;
  gap: 5px;
  flex-shrink: 0;
}

.task-list li .task-actions button {
  background: none;
  border: none;
  color: #95a5a6;
  cursor: pointer;
  padding: 3px;
  font-size: 0.9em;
}

.task-list li .task-actions button:hover {
  color: #ecf0f1;
}

.management-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.management-list li {
  padding: 10px 15px;
  cursor: pointer;
  border-bottom: 1px solid #34495e;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
  font-size: 0.95em;
}

.management-list li:hover,
.management-list li.active {
  background-color: #34495e;
}

.management-list li .management-item-name {
  display: flex;
  align-items: center;
}

.management-list li i {
  margin-right: 8px;
  width: 16px;
  text-align: center;
}

.main-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.main-header {
  padding: 10px 20px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
}

.task-name-input {
  flex-grow: 1;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1em;
  margin-right: 10px;
}

.run-button {
  background-color: #27ae60;
  color: white;
  border: none;
  padding: 8px 20px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 1em;
  cursor: pointer;
  border-radius: 4px;
}

.run-button:hover {
  background-color: #229954;
}

.main-tabs-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  padding: 0;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  padding: 0 20px;
  background-color: #f8f9fa;
  align-items: center;
}

.tab-button {
  padding: 12px 20px;
  cursor: pointer;
  border: none;
  background: none;
  font-size: 0.95em;
  border-bottom: 3px solid transparent;
  margin-bottom: -1px;
  transition: all 0.2s ease;
}

.tab-button.active {
  border-bottom: 3px solid #3498db;
  color: #3498db;
  font-weight: bold;
  background-color: #ffffff;
}

.tab-button:hover {
  background-color: #e9ecef;
}

.tab-content-wrapper {
  flex-grow: 1;
  overflow-y: auto;
}

.tab-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.log-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  gap: 15px;
}

.log-controls button {
  padding: 6px 12px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
}

.log-controls button:hover {
  background-color: #5a6268;
}

.log-controls div {
  display: flex;
  align-items: center;
  gap: 8px;
}

.log-controls input[type="checkbox"] {
  margin: 0;
}

.log-controls label {
  margin: 0;
  font-size: 0.9em;
  color: #666;
}

.log-filter {
  padding: 6px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 0.9em;
}

.status-bar {
  margin-bottom: 15px;
  padding: 15px 20px;
  background-color: #e9ecef;
  border-radius: 4px;
  font-size: 0.9em;
}

.status-bar span {
  margin-right: 15px;
}

.status-bar .status-code.success {
  color: #27ae60;
  font-weight: bold;
}

.status-bar .status-code.error {
  color: #c0392b;
  font-weight: bold;
}

.response-data {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  min-height: 400px;
  max-height: 600px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  margin: 0 20px 20px 20px;
  line-height: 1.4;
  display: flex;
  flex-direction: column;
}

.response-data .log-info {
  color: #3498db;
  display: block;
  margin-bottom: 5px;
}

.response-data .log-warning {
  color: #f39c12;
  display: block;
  margin-bottom: 5px;
}

.response-data .log-error {
  color: #e74c3c;
  display: block;
  margin-bottom: 5px;
}

.response-data .log-success {
  color: #27ae60;
  display: block;
  margin-bottom: 5px;
}

/* 日志条目样式 */
.log-entry {
  display: flex;
  margin-bottom: 4px;
  font-size: 0.9em;
  line-height: 1.4;
}

.log-timestamp {
  color: #666;
  margin-right: 10px;
  font-family: monospace;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.log-entry.log-info .log-message {
  color: #3498db;
}

.log-entry.log-success .log-message {
  color: #27ae60;
}

.log-entry.log-warning .log-message {
  color: #f39c12;
}

.log-entry.log-error .log-message {
  color: #e74c3c;
}

.log-empty {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

/* 历史记录样式 */
.history-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  flex-wrap: wrap;
}

.history-filter {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-width: 200px;
}

.history-summary {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

.history-items-container {
  max-height: 600px;
  overflow-y: auto;
}

.history-empty-state {
  text-align: center;
  color: #95a5a6;
  padding: 60px 20px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 2px dashed #dee2e6;
}

.history-empty-state .empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.history-empty-state h5 {
  margin: 0 0 8px 0;
  color: #6c757d;
}

.history-empty-state p {
  margin: 0;
  color: #adb5bd;
  font-size: 14px;
}

.history-item {
  padding: 15px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 10px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.history-item:hover {
  border-color: #3498db;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.history-task-name {
  font-weight: 500;
  font-size: 16px;
  color: #2c3e50;
}

.history-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.history-status.status-completed {
  background: #d4edda;
  color: #155724;
}

.history-status.status-failed {
  background: #f8d7da;
  color: #721c24;
}

.history-status.status-running {
  background: #fff3cd;
  color: #856404;
}

.history-item-details {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #6c757d;
  flex-wrap: wrap;
}

/* 图表样式 */
.charts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-task-name {
  font-size: 14px;
  color: #6c757d;
}

.charts-container {
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 20px;
}

.charts-controls {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.charts-summary {
  font-size: 14px;
  color: #6c757d;
}

.charts-stats {
  display: flex;
  gap: 15px;
  align-items: center;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #6c757d;
}

.stat-value {
  font-weight: 500;
  color: #495057;
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-card {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 15px;
  background: white;
}

.chart-card h6 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.chart-placeholder {
  position: relative;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 4px;
  color: #6c757d;
}

.chart-placeholder::before {
  content: '📊 图表占位符';
  font-size: 14px;
}

.charts-empty-state {
  text-align: center;
  color: #95a5a6;
  padding: 60px 20px;
}

.charts-empty-state .empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.charts-empty-state h5 {
  margin: 0 0 8px 0;
  color: #6c757d;
}

.charts-empty-state p {
  margin: 0;
  color: #adb5bd;
  font-size: 14px;
}

/* 按钮样式 */
.btn-primary {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-danger:hover {
  background-color: #c82333;
}

/* 轻量级滚动条样式 */
.response-data::-webkit-scrollbar,
.history-items-container::-webkit-scrollbar,
.log-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.response-data::-webkit-scrollbar-track,
.history-items-container::-webkit-scrollbar-track,
.log-container::-webkit-scrollbar-track {
  background: transparent;
}

.response-data::-webkit-scrollbar-thumb,
.history-items-container::-webkit-scrollbar-thumb,
.log-container::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.response-data::-webkit-scrollbar-thumb:hover,
.history-items-container::-webkit-scrollbar-thumb:hover,
.log-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.4);
}

.response-data::-webkit-scrollbar-corner,
.history-items-container::-webkit-scrollbar-corner,
.log-container::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox 滚动条样式 */
.response-data,
.history-items-container,
.log-container {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

/* 淡出动效 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}

/* 任务名称输入框的过渡动画 */
.task-name-input {
  transition: all 0.3s ease;
}

/* 标签页内容的过渡动画 */
.tab-content-wrapper {
  position: relative;
}

.tab-content {
  transition: opacity 0.3s ease;
}
</style>
