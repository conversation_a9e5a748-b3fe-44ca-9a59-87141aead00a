<template>
  <div class="config-container">
    <!-- 基本设置 -->
    <div class="form-section">
      <h4>基本设置</h4>
      <div class="form-group">
        <label for="taskDescription">任务描述</label>
        <input 
          type="text" 
          id="taskDescription" 
          v-model="taskConfig.description"
          placeholder="输入任务描述"
        />
      </div>
      <div class="form-group" style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
        <div>
          <label for="cycleDuration">周期时长 (秒)</label>
          <input 
            type="number" 
            id="cycleDuration" 
            v-model="taskConfig.cycleDuration"
          />
        </div>
        <div>
          <label for="totalDuration">总运行时长 (秒, 0为无限)</label>
          <input 
            type="number" 
            id="totalDuration" 
            v-model="taskConfig.totalDuration"
          />
        </div>
      </div>
    </div>

    <!-- 队列配置 -->
    <div class="form-section">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
        <h4>队列配置</h4>
        <button @click="addQueue" style="padding: 5px 10px; font-size: 0.9em;">＋ 添加队列</button>
      </div>
      <div id="queuesContainer">
        <div 
          v-for="(queue, index) in taskConfig.queues" 
          :key="index"
          class="queue-item"
        >
          <div class="queue-item-header">
            <h5>队列 {{ index + 1 }}</h5>
            <button @click="removeQueue(index)">✕</button>
          </div>
          <div class="form-group">
            <label>消息模板</label>
            <select v-model="queue.template">
              <option value="">选择模板...</option>
              <option value="order">订单创建模板</option>
              <option value="payment">支付成功模板</option>
              <option value="inventory">库存更新模板</option>
            </select>
          </div>
          <div class="form-group">
            <label>Topic (从模板自动获取)</label>
            <input 
              type="text" 
              v-model="queue.topic"
              readonly 
              style="background-color: #f8f9fa;"
              placeholder="请先选择消息模板"
            />
          </div>
          <div class="form-group">
            <label>每周期发送次数</label>
            <input 
              type="number" 
              v-model="queue.messagesPerCycle"
              min="1"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 环境配置 -->
    <div class="form-section">
      <h4>环境配置</h4>
      <div class="form-group">
        <label for="environmentSelect">选择环境</label>
        <select id="environmentSelect" v-model="taskConfig.environment">
          <option value="">选择环境...</option>
          <option value="dev">开发环境 (DEV)</option>
          <option value="staging">预发环境 (Staging)</option>
          <option value="prod">生产环境 (PROD)</option>
        </select>
      </div>
      <div class="form-group">
        <label for="envVariablesPreview">环境变量预览 (只读)</label>
        <textarea 
          id="envVariablesPreview" 
          readonly 
          rows="8" 
          style="background-color: #f8f9fa; font-family: 'Courier New', monospace; font-size: 13px; box-sizing: border-box;"
          :value="envPreview"
        ></textarea>
      </div>
    </div>

    <!-- Save Task Button Container -->
    <div style="text-align: right; margin-top: 10px; margin-bottom: 20px; padding-right: 5px;">
      <button class="save-task-btn" @click="saveTask" title="保存当前任务配置">
        💾 保存任务
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import api from '@/api'

// 任务配置数据
const taskConfig = ref({
  description: '',
  cycleDuration: 60,
  totalDuration: 300,
  environment: '',
  queues: []
})

// 环境列表和模板列表
const environments = ref([])
const templates = ref([])

// 环境变量预览
const envPreview = computed(() => {
  const envMap = {
    dev: 'KAFKA_BROKERS=kafka-dev:9092\nSCHEMA_REGISTRY_URL=http://schema-registry-dev:8081\nTIMEOUT=5000\nCLIENT_ID=dev-client',
    staging: 'KAFKA_BROKERS=kafka-staging:9092\nSCHEMA_REGISTRY_URL=http://schema-registry-staging:8081\nTIMEOUT=10000\nCLIENT_ID=staging-client',
    prod: 'KAFKA_BROKERS=kafka-prod:9092\nSCHEMA_REGISTRY_URL=http://schema-registry-prod:8081\nTIMEOUT=15000\nCLIENT_ID=prod-client'
  }
  return envMap[taskConfig.value.environment] || ''
})

// 模板到Topic的映射
const templateTopicMap = {
  order: 'orders.new',
  payment: 'payments.success',
  inventory: 'inventory.update'
}

// 监听模板选择变化，自动更新Topic
watch(() => taskConfig.value.queues, (queues) => {
  queues.forEach(queue => {
    if (queue.template && templateTopicMap[queue.template]) {
      queue.topic = templateTopicMap[queue.template]
    }
  })
}, { deep: true })

// 添加队列
const addQueue = () => {
  taskConfig.value.queues.push({
    template: '',
    topic: '',
    messagesPerCycle: 10
  })
}

// 删除队列
const removeQueue = (index) => {
  taskConfig.value.queues.splice(index, 1)
}

// 保存任务
const saveTask = () => {
  console.log('保存任务配置:', taskConfig.value)
  // TODO: 实现保存逻辑
}
</script>

<style scoped>
.config-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 表单区域样式 */
.form-section {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease;
}

.form-section:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.form-section h4 {
  font-size: 1.3em;
  color: #2c3e50;
  margin-top: 0;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
  font-weight: 600;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  font-size: 0.9em;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 0.95em;
}

.form-group textarea {
  min-height: 80px;
  resize: vertical;
}

/* 队列项样式 */
.queue-item {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
  border-radius: 4px;
  margin-bottom: 10px;
  background-color: #f9f9f9;
}

.queue-item .form-group {
  margin-bottom: 5px;
}

.queue-item-header {
  grid-column: 1 / -1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.queue-item-header h5 {
  margin: 0;
  font-size: 1.1em;
  font-weight: 600;
  color: #333;
}

.queue-item-header button {
  background: none;
  border: none;
  color: #c0392b;
  cursor: pointer;
  font-size: 1.2em;
  padding: 2px 6px;
}

.queue-item-header button:hover {
  background-color: #f8d7da;
  border-radius: 3px;
}

/* 保存按钮样式 */
.save-task-btn {
  padding: 8px 16px;
  background-color: #27ae60;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.save-task-btn:hover {
  background-color: #229954;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.save-task-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
